<script setup>
import Layout from '@spa/pages/Layouts/Layout';
import { Head, usePage } from '@inertiajs/vue3';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import { computed } from 'vue';
import StaffsListComponent from '@spa/modules/users/staffs/StaffsListComponent.vue';
import AgentsListComponent from '@spa/modules/users/agents/AgentsListComponent.vue';
import EmployersListComponent from '@spa/modules/users/employers/EmployersListComponent.vue';
import ServiceProvidersListComponent from '@spa/modules/users/service-providers/ServiceProvidersListComponent.vue';
import PlacementProvidersListComponent from '@spa/modules/users/placement-providers/PlacementProvidersListComponent.vue';
import TrainersListComponent from '@spa/modules/users/trainers/TrainersListComponent.vue';
import AgentStaffsListComponent from '@spa/modules/users/agents-staff/AgentStaffsListComponent.vue';
import StudentsListComponent from '@spa/modules/users/students/StudentsListComponent.vue';

const $page = usePage();

const activeTab = computed(() => {
    const path = $page.url;
    const slug = path.split('/').pop();
    if (!isNaN(Number(slug))) {
        return 'team-members';
    }
    return slug || 'team-members';
});

const tabs = [
    {
        name: 'Team Members(Staffs)',
        slug: 'team-members',
        route: route('spa.manage-users.team-members'),
    },
    {
        name: 'Trainers(Teachers)',
        slug: 'trainers',
        route: route('spa.manage-users.trainers'),
    },
    {
        name: 'Agent Staffs',
        slug: 'agent-staffs',
        route: route('spa.manage-users.agent-staffs'),
    },
    {
        name: 'Education Agency(Agents)',
        slug: 'agents',
        route: route('spa.manage-users.agents'),
    },
    {
        name: 'Employers',
        slug: 'employers',
        route: route('spa.manage-users.employers'),
    },
    {
        name: 'Service Providers',
        slug: 'service-providers',
        route: route('spa.manage-users.service-providers'),
    },
    {
        name: 'Placement Providers',
        slug: 'placement-providers',
        route: route('spa.manage-users.placement-providers'),
    },
    {
        name: 'Students',
        slug: 'students',
        route: route('spa.manage-users.students'),
    },
];
</script>
<template>
    <Layout
        :no-spacing="true"
        :loading="false"
        :title="'Manage Users'"
        :show-header="true"
        :show-tabs="true"
        :tabs="tabs"
        :active-tab="activeTab"
    >
        <Head title="Manage Users" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Manage Users" :back="false" />
        </template>
        <div class="flex h-full flex-col px-8 py-6">
            <StaffsListComponent v-if="activeTab === 'team-members'" />
            <TrainersListComponent v-if="activeTab === 'trainers'" />
            <AgentsListComponent v-if="activeTab === 'agents'" />
            <AgentStaffsListComponent v-if="activeTab === 'agent-staffs'" />
            <EmployersListComponent v-if="activeTab === 'employers'" />
            <ServiceProvidersListComponent v-if="activeTab === 'service-providers'" />
            <PlacementProvidersListComponent v-if="activeTab === 'placement-providers'" />
            <StudentsListComponent v-if="activeTab === 'students'" />
        </div>
    </Layout>
</template>

<style scoped></style>
