<?php

namespace App\Repositories;

use App\Exceptions\ApplicationException;
use App\Helpers\Helpers;
use App\Model\v2\Agent;
use App\Model\v2\AttendanceLog;
use App\Model\v2\CollegeCampus;
use App\Model\v2\Country;
use App\Model\v2\Courses;
use App\Model\v2\CoursesIntakeDate;
use App\Model\v2\CourseSubject;
use App\Model\v2\CourseType;
use App\Model\v2\LetterParameter;
use App\Model\v2\Roles;
use App\Model\v2\SetupSection;
use App\Model\v2\Staff;
use App\Model\v2\StaffCommunicationLog;
use App\Model\v2\Student;
use App\Model\v2\StudentAttendance;
use App\Model\v2\StudentCommunicationLog;
use App\Model\v2\StudentCourseAttendance;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentInitialPayment;
use App\Model\v2\StudentSubjectEnrolment;
use App\Model\v2\Timetable;
use App\Model\v2\TimetableDetail;
use App\Model\v2\VisaStatus;
use App\Services\ScoutFilterService;
use App\Traits\CommonTrait;
use App\Traits\LetterGenerateAndSendTrait;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Meilisearch\Endpoints\Indexes;
use Support\Services\UploadService;

class StudentRepository extends CommonRepository
{
    use CommonTrait;
    use LetterGenerateAndSendTrait;

    protected $model;

    public function __construct(Model $model)
    {
        $this->model = $model;
        $this->studentProfileCommonRepository = new StudentProfileCommonRepository;
    }

    public function with($relations)
    {
        return $this->model->with($relations);
    }

    public function Where($whereArr)
    {
        return $this->model->where($whereArr);
    }

    public function getStudentDataV2($request, $countOnly = false)
    {
        $collegeId = $request->user()->college_id;
        $post = ($request->input()) ? $request->input() : [];
        $studentRoleType = Roles::TYPE_STUDENT;
        $columnArr = [
            'rs.id',
            'rs.profile_picture',
            'rs.first_name',
            'rs.family_name',
            DB::raw("TRIM(CONCAT(rs.first_name,' ',rs.family_name)) as student_name"),
            'rs.student_type',
            DB::raw("GROUP_CONCAT(CONCAT_WS(' - ', rc.course_code, rc.course_name) SEPARATOR ',@@,') AS course_list"),
            // DB::raw("GROUP_CONCAT(CONCAT(rsc.status,'##', rc.course_code, ' - ', rc.course_name) SEPARATOR ',@@,') AS course_list"),
            DB::raw('GROUP_CONCAT(rsc.id) AS rsc_id_list'),
            'ra.agency_name',
            DB::raw("DATE_FORMAT(rs.DOB, '%d %b %Y') as formatted_dob"),
            'rs.DOB',
            'rs.USI',
            'rs.email',
            'rs.birthplace',
            'rs.current_mobile_phone as contact',
            'rsd.emergency_contact_person as emergency',
            'rsd.emergency_phone',
            DB::raw("(CASE WHEN rs.student_type = 'Offshore' THEN 'Overseas Student' ELSE (CASE WHEN rs.student_type = 'Onshore' THEN 'Overseas Student in Australia' ELSE 'Resident Student' END) END) as stud_type_name"),
            DB::raw("CONCAT_WS(', ', rs.current_street_no, rs.current_street_name, rs.current_city, rs.current_state, rs.current_postcode, country.name) as address"),
            'generated_stud_id as student_id',
            'campus.name as campus',
            'ru.username',
            'college.college_name',
            DB::raw('GROUP_CONCAT(DISTINCT rsc.course_id SEPARATOR ",") AS course_id'),
        ];

        $query = Student::alias('rto_students as rs')
            ->leftjoin('rto_users as ru', function ($join) use ($studentRoleType) {
                $join->on('ru.username', '=', 'rs.generated_stud_id');
                $join->on('ru.role_id', '=', DB::raw($studentRoleType));
            })
            ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rs.id')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->leftjoin('rto_campus as campus', 'campus.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_country as country', 'country.id', '=', 'rs.current_country')
            ->leftjoin('rto_colleges as college', 'college.id', '=', 'rs.college_id')
            ->where(['rs.college_id' => $collegeId, 'is_student' => 1])
            ->select($columnArr)
            ->groupBy('rs.id');

        $result = $this->gridDataPagination($query, $post, $countOnly);

        if (! $countOnly) {
            return $this->getStudentMoreDetails($result);
        }

        return $result;
    }

    public function getStudentMoreDetails($result)
    {
        foreach ($result as $k => $row) {
            $result[$k]['profile_pic'] = $this->getStudentProfilePicPath($row['id'], $row['profile_picture']);
            $result[$k]['mini_profile_pic'] = $this->getStudentProfilePicPath($row['id'], $row['profile_picture'], 'small');

            $courseIdList = explode(',', $row['course_id']);
            $result[$k]['course_id'] = array_map('intval', $courseIdList);

            $studentCourseIds = explode(',', $row['rsc_id_list']);
            if (count($studentCourseIds) > 0) {

                $course_detail = StudentCourses::from('rto_student_courses as t1')
                    ->leftjoin('rto_courses as t2', 't2.id', '=', 't1.course_id')
                    ->whereIn('t1.id', $studentCourseIds)
                    ->select(
                        't1.id',
                        't2.college_id',
                        't1.student_id',
                        't1.course_id',
                        't2.course_code',
                        't2.course_name',
                        't1.offer_status',
                        't1.status',
                        DB::raw('DATE_FORMAT(t1.start_date, "%d %b, %Y") as start_date'),
                        DB::raw('DATE_FORMAT(t1.finish_date, "%d %b, %Y") as finish_date'),
                        DB::raw('(CASE WHEN (t1.finish_date > now() AND t1.start_date > now()) THEN 0 WHEN t1.finish_date > now() THEN '.dbRawL10('DATEDIFF(now(), t1.start_date)').' ELSE '.dbRawL10('DATEDIFF(t1.finish_date, t1.start_date)').' END) as days'),
                        DB::raw('DATEDIFF(t1.finish_date, t1.start_date) as diff_days')
                    )
                    ->orderBy(DB::raw('(CASE WHEN t1.status = "Current Student" THEN "a1" WHEN t1.status = "Enrolled" THEN "a2" ELSE t1.status END)'), 'ASC')
                    ->get()
                    ->toArray();

                $attendance_detail = [];
                foreach ($course_detail as $k1 => $course) {
                    if ($k1 == 0) {
                        $whereArr = [
                            'a1.college_id' => $course['college_id'],
                            'a1.student_id' => $course['student_id'],
                            'a1.course_id' => $course['course_id'],
                        ];
                        $whereArr2 = [
                            'a1.college_id' => $course['college_id'],
                            'a1.course_id' => $course['course_id'],
                        ];

                        $courseSubjectWhereArr = [
                            'rto_course_subject.college_id' => $course['college_id'],
                            'rto_course_subject.course_id' => $course['course_id'],
                        ];

                        // For Course Progress Column
                        $today = date('Y-m-d');
                        $course_progress = StudentSubjectEnrolment::from('rto_student_subject_enrolment as a1')
                            ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'a1.unit_id')
                            ->where($whereArr)
                            ->select(
                                'a1.id',
                                'a1.unit_id',
                                'a1.final_outcome',
                                'rsu.unit_name as unit_title',
                                DB::raw("CONCAT(rsu.vet_unit_code,' : ',rsu.unit_name) as unit_name"),
                                DB::raw('(CASE WHEN a1.final_outcome = "C" THEN "green-500" ELSE (CASE WHEN a1.final_outcome = "NYC" THEN "red-500" ELSE "gray-200" END) END) as color'),
                                DB::raw('(CASE WHEN a1.activity_start_date <= "'.$today.'" AND a1.activity_finish_date >= "'.$today.'" THEN 1 ELSE 0 END) as is_active')
                            )
                            ->groupBy('a1.id')
                            ->get()
                            ->toArray();

                        if (count($course_progress) == 0) {

                            $course_progress = CourseSubject::from('rto_course_subject')
                                ->join('rto_subject_unit as rsu', 'rsu.subject_id', '=', 'rto_course_subject.subject_id')
                                ->where($courseSubjectWhereArr)
                                ->select(
                                    'rto_course_subject.id',
                                    'rto_course_subject.subject_id as unit_id',
                                    DB::raw("CONCAT(rsu.vet_unit_code,' : ',rsu.unit_name) as unit_name"),
                                    DB::raw('(CASE WHEN rto_course_subject.id > 0 THEN "gray-200" END) as color')
                                )
                                ->get()
                                ->toArray();
                        }

                        // $final_course_progress = array_merge($course_progress, $course_progress2);
                        $key_values = array_column($course_progress, 'id');
                        array_multisort($key_values, SORT_DESC, $course_progress);
                        $course_detail[$k1]['course_progress'] = $course_progress;

                        $attendanceResult = $this->getStudentAttendanceData($whereArr);

                        // For Attendance Column
                        $course_detail[$k1]['course_attendance'] = $attendanceResult['course_attendance'];

                        // For Attendance details only for mini profile
                        $attendance_detail[$k1]['course_attendance'] = $attendanceResult['calculated_attendance'];

                        // For Current Course Column
                        $current_course = StudentSubjectEnrolment::from('rto_student_subject_enrolment as a1')
                            ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'a1.unit_id')
                            ->where($whereArr)
                            ->select(
                                DB::raw('(SUM(CASE WHEN a1.final_outcome != "" THEN 1 ELSE 0 END)) as total_unit'),
                                DB::raw('(SUM(CASE WHEN a1.final_outcome = "C" OR a1.final_outcome = "NYC" THEN 1 ELSE 0 END)) as use_unit'),
                                DB::raw('CONCAT(SUM(CASE WHEN a1.final_outcome = "C" THEN 1 ELSE 0 END), " C, ", SUM(CASE WHEN a1.final_outcome = "NYC" THEN 1 ELSE 0 END), " NYC") as title')
                            )
                            ->groupBy('a1.course_id')
                            ->get()
                            ->toArray();

                        if (count($current_course) == 0) {
                            $current_course = CourseSubject::from('rto_course_subject')
                                ->join('rto_subject_unit as rsu', 'rsu.subject_id', '=', 'rto_course_subject.subject_id')
                                ->where($courseSubjectWhereArr)
                                ->select(
                                    DB::raw('(COUNT(rto_course_subject.id)) as total_unit'),
                                    DB::raw('(CASE WHEN rto_course_subject.id > 0 THEN 0 ELSE 0 END) as use_unit'),
                                    DB::raw('(CASE WHEN rto_course_subject.id > 0 THEN "0 C, 0 NYC" ELSE "0 C, 0 NYC" END) as title')
                                )
                                ->get()
                                ->toArray();
                            if (count($current_course) == 0) {
                                $current_course = [['title' => '0 C, 0 NYC', 'total_unit' => 0, 'use_unit' => 0]];
                            }
                        }
                        $course_detail[$k1]['current_course'] = $current_course;

                        // For Payment Column
                        $course_payment = StudentInitialPayment::from('rto_student_initial_payment')
                            ->join('rto_student_initial_payment_details as a2', 'a2.student_course_id', '=', 'rto_student_initial_payment.student_course_id')
                            // ->where($whereArr)
                            ->where([
                                'rto_student_initial_payment.college_id' => $course['college_id'],
                                'rto_student_initial_payment.student_id' => $course['student_id'],
                                'rto_student_initial_payment.course_id' => $course['course_id'],
                            ])
                            ->select(
                                DB::raw('rto_student_initial_payment.tution_fee as total'),
                                DB::raw('(SUM(CASE When a2.payment_status != "unpaid" THEN a2.upfront_fee_pay ELSE 0 END)) as paid'),
                                DB::raw('(SUM(CASE When a2.payment_status != "paid" AND a2.due_date > now() THEN (a2.upfront_fee_to_pay  - a2.upfront_fee_pay) ELSE 0 END)) as unpaid'),
                                DB::raw('(SUM(CASE When a2.payment_status != "paid" AND a2.due_date < now() THEN (a2.upfront_fee_to_pay - a2.upfront_fee_pay) ELSE 0 END)) as missed'),
                                // DB::raw('(COUNT(a2.id)) as total_count'),
                                DB::raw('(SUM(CASE When a2.payment_status = "paid" THEN 1 ELSE 0 END)) as paid_count'),
                                DB::raw('(SUM(CASE When a2.payment_status != "paid" AND a2.due_date > now() THEN 1 ELSE 0 END)) as unpaid_count'),
                                DB::raw('(SUM(CASE When a2.payment_status != "paid" AND a2.due_date < now() THEN 1 ELSE 0 END)) as missed_count')
                            )
                            ->get()
                            ->toArray();
                        $course_detail[$k1]['course_payment'] = $course_payment;
                    }
                }

                $result[$k]['course_detail'] = $course_detail;
                $result[$k]['attendance_detail'] = $attendance_detail;
            }
        }

        return $result;
    }

    private function getDays($sStartDate, $sEndDate)
    {
        // Firstly, format the provided dates.
        // This function works best with YYYY-MM-DD
        // but other date formats will work thanks
        // to strtotime().
        $sStartDate = gmdate('Y-m-d', strtotime($sStartDate));
        $sEndDate = gmdate('Y-m-d', strtotime($sEndDate));

        // Start the variable off with the start date
        $aDays[date('l', strtotime($sStartDate))] = $sStartDate;

        // Set a 'temp' variable, sCurrentDate, with
        // the start date - before beginning the loop
        $sCurrentDate = $sStartDate;

        // While the current date is less than the end date
        while ($sCurrentDate < $sEndDate) {
            // Add a day to the current date
            $sCurrentDate = gmdate('Y-m-d', strtotime('+1 day', strtotime($sCurrentDate)));

            // Add this new day to the aDays array
            $aDays[date('l', strtotime($sCurrentDate))] = $sCurrentDate;
        }

        // Once the loop has finished, return the
        // array of days.
        return $aDays;
    }

    public function getStudentIdBasedOnTeachers($teacherIds)
    {
        $collegeId = Auth::user()->college_id;
        if (count($teacherIds) > 0) {
            $batchArr = Timetable::Where(['college_id' => $collegeId])
                ->whereIn('teacher_id', $teacherIds)
                ->groupBy('batch')
                ->get(['batch'])
                ->toArray();
            $batchIdArr = array_column($batchArr, 'batch');

            $result = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                ->Join('rto_student_courses as rsc', function ($join) {
                    $join->on('rsc.student_id', '=', 'rsse.student_id');
                    $join->on('rsc.course_id', '=', 'rsse.course_id');
                })
                ->join('rto_students as rs', 'rs.id', '=', 'rsse.student_id')
                ->whereIn('rsse.batch', $batchIdArr)
                ->select('rsse.student_id')
                ->groupBy('rs.id')
                ->get()
                ->toArray();
            $studentIdArr = array_column($result, 'student_id');

            return $studentIdArr;
        }
    }

    public function getStudentIdBasedOnBatch($batch)
    {
        if (count($batch) > 0) {
            $result = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                ->leftjoin('rto_timetable as rt', function ($join) {
                    $join->on('rt.subject_id', '=', 'rsse.subject_id');
                    $join->on('rt.batch', '=', 'rsse.batch');
                })
                ->Join('rto_student_courses as rsc', function ($join) {
                    $join->on('rsc.student_id', '=', 'rsse.student_id');
                    $join->on('rsc.course_id', '=', 'rsse.course_id');
                })
                ->join('rto_students as rs', 'rs.id', '=', 'rsse.student_id')
                ->whereIn('rt.batch', $batch)
                ->select('rsse.student_id')
                ->groupBy('rs.id')
                ->get()
                ->toArray();

            $studentIdArr = array_column($result, 'student_id');

            return $studentIdArr;
        }
    }

    public function getStudentIdBasedOnIntakeDate($intakeData)
    {
        $studentIds = [];
        if (count($intakeData) > 0) {
            $collegeId = Auth::user()->college_id;

            $query = Student::alias('rto_students as rs')
                ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
                ->where(['rs.college_id' => $collegeId, 'is_student' => 1])
                ->select(['rs.id']);

            $query->where(function ($childQuery) use ($intakeData) {
                foreach ($intakeData as $intakeStr) {
                    [$intakeMethod, $intakeDate] = explode('@', $intakeStr);
                    if ($intakeMethod == 'new') {
                        $childQuery->where('rsc.intake_date', $intakeDate);
                    } elseif ($intakeMethod == 'between') {
                        $childQuery->whereBetween('rsc.intake_date', [$intakeDate, date('Y-m-d')]);
                    } elseif ($intakeMethod == 'before') {
                        $childQuery->where('rsc.intake_date', '<', $intakeDate);
                    } elseif ($intakeMethod == 'after') {
                        $childQuery->where('rsc.intake_date', '>', $intakeDate);
                    }
                }
            });

            $result = $query->groupBy('rs.id')->get()->toArray();
            $studentIds = array_column($result, 'id');
        }

        return $studentIds;
    }

    public function getStudentData($request, $countOnly = false)
    {

        $collegeId = $request->user()->college_id;
        $post = ($request->input()) ? $request->input() : [];
        $customFilterParts = (isset($post['filter']) && isset($post['filter']['filters'])) ? $post['filter']['filters'] : [];
        $customFilterParts = $this->manageFilterSubQueryData($collegeId, $customFilterParts);

        $columnArr = [
            'rs.id',
            'rs.profile_picture',
            DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as student_name"),
            'rs.student_type',
            DB::raw("GROUP_CONCAT(CONCAT_WS(' - ', rc.course_code, rc.course_name) SEPARATOR ',@@,') AS course_list"),
            // DB::raw("GROUP_CONCAT(CONCAT(rsc.status,'##', rc.course_code, ' - ', rc.course_name) SEPARATOR ',@@,') AS course_list"),
            DB::raw('GROUP_CONCAT(rsc.id) AS rsc_id_list'),
            'ra.agency_name',
            'rs.DOB',
            'rs.USI',
            'rs.email',
            'rs.birthplace',
            'rs.current_mobile_phone as contact',
            'rsd.emergency_contact_person as emergency',
            'rsd.emergency_phone',
            DB::raw("(CASE WHEN rs.student_type = 'Offshore' THEN 'Overseas Student' ELSE (CASE WHEN rs.student_type = 'Onshore' THEN 'Overseas Student in Australia' ELSE 'Resident Student' END) END) as stud_type_name"),
            DB::raw("CONCAT_WS(', ', rs.current_street_no, rs.current_street_name, rs.current_city, rs.current_state, rs.current_postcode, country.name) as address"),
            'generated_stud_id as student_id',
            'campus.name as campus',
        ];

        /* This array use for filterable only */
        $columns = [
            'student_name' => DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as student_name"),
            'course_list' => DB::raw("CONCAT(rc.course_code,' - ',rc.course_name) as course_list"),
            'student_type' => 'rs.student_type',
            'DOB' => 'rs.DOB',
            'student_id' => 'generated_stud_id as student_id',
            'campus' => 'campus.name as campus',
        ];

        $query = Student::alias('rto_students as rs')
            ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rs.id')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->leftjoin('rto_campus as campus', 'campus.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_country as country', 'country.id', '=', 'rs.current_country')
            ->where(['rs.college_id' => $collegeId, 'is_student' => 1])
            ->select($columnArr);

        // $query->whereIn('rs.id', [17,19]);
        // $query->whereIn("rs.id", [117,150,155,170,223,267,287,469]);
        // $query->whereIn("rs.id", [20,33,101,103,117,119,122,519,521]);

        $this->gridDataFilter($query, $post, $columns);

        foreach ($customFilterParts as $filter) {
            if ($filter['field'] == 'extra' && isset($filter['value'])) {
                /* grid wise custom filter apply here */
                $query->where(function ($childQuery) use ($filter) {
                    foreach ($filter['value'] as $fieldName => $fieldValue) {
                        if (count($fieldValue) > 0) {
                            if ($fieldName == 'course_id') {
                                $childQuery->whereIn('rc.id', $fieldValue);
                            } elseif ($fieldName == 'student_intake') {
                                $childQuery->where(function ($subChildQuery) use ($fieldValue) {
                                    foreach ($fieldValue as $intakeStr) {
                                        $intake = explode('@', $intakeStr);
                                        $subChildQuery->orWhere(function ($subChildQuery2) use ($intake) {
                                            if ($intake[0] == 'new') {
                                                $subChildQuery2->where('rsc.intake_date', $intake[1]);
                                            } elseif ($intake[0] == 'between') {
                                                $subChildQuery2->whereBetween('rsc.intake_date', $intake[1]);
                                            } elseif ($intake[0] == 'before') {
                                                $subChildQuery2->where('rsc.intake_date', '<', $intake[1]);
                                            } elseif ($intake[0] == 'after') {
                                                $subChildQuery2->where('rsc.intake_date', '>', $intake[1]);
                                            }
                                        });
                                    }
                                });
                            } elseif ($fieldName == 'student_type') {
                                $childQuery->whereIn('rs.student_type', $fieldValue);
                            } elseif ($fieldName == 'nationality') {
                                $childQuery->whereIn('rs.current_country', $fieldValue);
                            } elseif ($fieldName == 'status') {
                                $childQuery->whereIn('rsc.status', $fieldValue);
                            } elseif ($fieldName == 'teacher_students') {
                                $childQuery->whereIn('rs.id', $fieldValue);
                            }
                        }
                        if ($fieldName == 'batch_students') {
                            $childQuery->whereIn('rs.id', $fieldValue);
                        }
                    }
                });
            }
        }

        $this->gridDataSorting($query, $post);

        $query->groupBy('rs.id');

        $result = $this->gridDataPagination($query, $post, $countOnly);

        if (! $countOnly) {
            foreach ($result as $k => $row) {
                $studentCourseIds = explode(',', $row['rsc_id_list']);
                if (count($studentCourseIds) > 0) {

                    $course_detail = StudentCourses::from('rto_student_courses as t1')
                        ->leftjoin('rto_courses as t2', 't2.id', '=', 't1.course_id')
                        ->whereIn('t1.id', $studentCourseIds)
                        ->select(
                            't1.id',
                            't2.college_id',
                            't1.student_id',
                            't1.course_id',
                            't2.course_code',
                            't2.course_name',
                            't1.offer_status',
                            't1.status',
                            DB::raw('DATE_FORMAT(t1.start_date, "%d %b, %Y") as start_date'),
                            DB::raw('DATE_FORMAT(t1.finish_date, "%d %b, %Y") as finish_date'),
                            DB::raw('(CASE WHEN (t1.finish_date > now() AND t1.start_date > now()) THEN 0 WHEN t1.finish_date > now() THEN '.dbRawL10('DATEDIFF(now(), t1.start_date)').' ELSE '.dbRawL10('DATEDIFF(t1.finish_date, t1.start_date)').' END) as days'),
                            DB::raw('DATEDIFF(t1.finish_date, t1.start_date) as diff_days')
                        )
                        ->orderBy(DB::raw('(CASE WHEN t1.status = "Current Student" THEN "a1" WHEN t1.status = "Enrolled" THEN "a2" ELSE t1.status END)'), 'ASC')
                        ->get()->toArray();

                    foreach ($course_detail as $k1 => $course) {
                        $whereArr = [
                            'a1.college_id' => $course['college_id'],
                            'a1.student_id' => $course['student_id'],
                            'a1.course_id' => $course['course_id'],
                        ];

                        $today = date('Y-m-d');
                        $course_progress = StudentSubjectEnrolment::from('rto_student_subject_enrolment as a1')
                            ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'a1.unit_id')
                            ->where($whereArr)
                            ->select(
                                'a1.id',
                                'a1.unit_id',
                                'a1.final_outcome',
                                'rsu.unit_name as unit_title',
                                DB::raw("CONCAT(rsu.vet_unit_code,' : ',rsu.unit_name) as unit_name"),
                                DB::raw('(CASE WHEN a1.final_outcome = "C" THEN "green-500" ELSE (CASE WHEN a1.final_outcome = "NYC" THEN "red-500" ELSE "gray-200" END) END) as color'),
                                DB::raw('(CASE WHEN a1.activity_start_date <= "'.$today.'" AND a1.activity_finish_date >= "'.$today.'" THEN 1 ELSE 0 END) as is_active')
                            )
                            ->groupBy('a1.id')
                            ->get()->toArray();

                        // if(count($course_progress) == 0){
                        $course_progress2 = CourseSubject::from('rto_course_subject as rcs')
                            ->join('rto_subject_unit as rsu', 'rsu.subject_id', '=', 'rcs.subject_id')
                            ->where([
                                'rcs.college_id' => $course['college_id'],
                                'rcs.course_id' => $course['course_id'],
                            ])
                            ->select(
                                'rcs.id',
                                'rcs.subject_id as unit_id',
                                DB::raw("CONCAT(rsu.vet_unit_code,' : ',rsu.unit_name) as unit_name"),
                                DB::raw('(CASE WHEN rcs.id > 0 THEN "gray-200" END) as color')
                            )
                            ->get()->toArray();
                        // }
                        $final_course_progress = array_merge($course_progress, $course_progress2);
                        $key_values = array_column($final_course_progress, 'id');
                        array_multisort($key_values, SORT_DESC, $final_course_progress);
                        $course_detail[$k1]['course_progress'] = $final_course_progress;

                        $course_attendance = StudentCourseAttendance::from('rto_student_course_attendance as a1')
                            ->where($whereArr)
                            ->select(DB::raw('AVG(a1.total_attd_per) as overall_attd'), DB::raw('AVG(a1.projected_attd_per) as projected_attd'))
                            ->get()->toArray();
                        if (count($course_attendance) == 0) {
                            $course_attendance = [['overall_attd' => 0, 'projected_attd' => 0]];
                        }

                        $attendance_detail[$k1]['course_attendance'] = $course_attendance[0];

                        if ($k1 == 0) {
                            /*
                    //                            $batchArrData = Timetable::from('rto_timetable as rt')
                    //                                ->leftjoin('rto_student_subject_enrolment as a1', 'a1.batch', '=', 'rt.batch')
                    //                                //->leftjoin('rto_student_subject_enrolment as a1', function($join) {
                    //                                    //$join->on('a1.subject_id', '=', 'rt.subject_id');
                    //                                    //$join->on('a1.batch', '=', 'rt.batch');
                    //                                //})
                    //                                ->where($whereArr)
                    //                                ->select(
                    //                                    'rt.id','rt.start_week', 'rt.end_week', 'rt.day'
                    //                                )
                    //                                ->get()->toArray();
                    //                            $batchArr = count($batchArrData);
                    //                            if($batchArr > 0){
                    //                                //dd($batchArr);
                    //                            }
                                                //$batchArr = implode(',', array_column($batchArrData, 'id'));

                                                //$batchArrData = StudentSubjectEnrolment::from('rto_student_subject_enrolment as a1')->where($whereArr)->select('a1.batch')->get()->toArray();
                                                //$batchArr = array_column($batchArrData, 'batch');
                                                //dd($batchArr);

                    //                            $course_attendance = StudentSubjectEnrolment::from('rto_student_subject_enrolment as a1')
                    //                                ->leftjoin('rto_timetable as rt', function($join) {
                    //                                    $join->on('rt.subject_id', '=', 'a1.subject_id');
                    //                                    $join->on('rt.batch', '=', 'a1.batch');
                    //                                })
                    //                                ->join('rto_student_attendance as rsa', 'rsa.timetable_id', '=', 'rt.id')
                    //                                ->where($whereArr)
                    //                                ->whereNotNull('rsa.week_period')   //TODO:: GNG-1843
                    //                                ->select('rt.id')
                    //                                ->get()->toArray();
                                                //dd($course_attendance);
                                                //$course_detail[$k1]['course_attendance'] = array_column($course_attendance, 'id');
                    //                            $course_detail[$k1]['course_attendance'] = $batchArr;*/

                            $current_course = StudentSubjectEnrolment::from('rto_student_subject_enrolment as a1')
                                ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'a1.unit_id')
                                ->where($whereArr)
                                ->select(
                                    DB::raw('(SUM(CASE WHEN a1.final_outcome != "" THEN 1 ELSE 0 END)) as total_unit'),
                                    DB::raw('(SUM(CASE WHEN a1.final_outcome = "C" OR a1.final_outcome = "NYC" THEN 1 ELSE 0 END)) as use_unit'),
                                    DB::raw('CONCAT(SUM(CASE WHEN a1.final_outcome = "C" THEN 1 ELSE 0 END), " C, ", SUM(CASE WHEN a1.final_outcome = "NYC" THEN 1 ELSE 0 END), " NYC") as title')
                                )
                                ->groupBy('a1.course_id')
                                ->get()->toArray();

                            if (count($current_course) == 0) {
                                $current_course = CourseSubject::from('rto_course_subject as rcs')
                                    ->join('rto_subject_unit as rsu', 'rsu.subject_id', '=', 'rcs.subject_id')
                                    ->where([
                                        'rcs.college_id' => $course['college_id'],
                                        'rcs.course_id' => $course['course_id'],
                                    ])
                                    ->select(
                                        DB::raw('(COUNT(rcs.id)) as total_unit'),
                                        DB::raw('(CASE WHEN rcs.id > 0 THEN 0 ELSE 0 END) as use_unit'),
                                        DB::raw('(CASE WHEN rcs.id > 0 THEN "0 C, 0 NYC" ELSE "0 C, 0 NYC" END) as title')
                                    )
                                    ->get()->toArray();
                                if (count($current_course) == 0) {
                                    $current_course = [['title' => '0 C, 0 NYC', 'total_unit' => 0, 'use_unit' => 0]];
                                }
                            }
                            $course_detail[$k1]['current_course'] = $current_course;

                            $course_payment = StudentInitialPayment::from('rto_student_initial_payment')
                                ->join('rto_student_initial_payment_details as a2', 'a2.student_course_id', '=', 'rto_student_initial_payment.student_course_id')
                                // ->where($whereArr)
                                ->where([
                                    'rto_student_initial_payment.college_id' => $course['college_id'],
                                    'rto_student_initial_payment.student_id' => $course['student_id'],
                                    'rto_student_initial_payment.course_id' => $course['course_id'],
                                ])
                                ->select(
                                    DB::raw('rto_student_initial_payment.tution_fee as total'),
                                    DB::raw('(SUM(CASE When a2.payment_status != "unpaid" THEN a2.upfront_fee_pay ELSE 0 END)) as paid'),
                                    DB::raw('(SUM(CASE When a2.payment_status != "paid" AND a2.due_date > now() THEN (a2.upfront_fee_to_pay - a2.upfront_fee_pay) ELSE 0 END)) as unpaid'),
                                    DB::raw('(SUM(CASE When a2.payment_status != "paid" AND a2.due_date < now() THEN (a2.upfront_fee_to_pay - a2.upfront_fee_pay) ELSE 0 END)) as missed'),
                                    // DB::raw('(COUNT(a2.id)) as total_count'),
                                    DB::raw('(SUM(CASE When a2.payment_status = "paid" THEN 1 ELSE 0 END)) as paid_count'),
                                    DB::raw('(SUM(CASE When a2.payment_status != "paid" AND a2.due_date > now() THEN 1 ELSE 0 END)) as unpaid_count'),
                                    DB::raw('(SUM(CASE When a2.payment_status != "paid" AND a2.due_date < now() THEN 1 ELSE 0 END)) as missed_count')
                                )
                                ->get()->toArray();

                            $course_detail[$k1]['course_payment'] = $course_payment;
                        }
                    }

                    $result[$k]['course_detail'] = $course_detail;
                    $result[$k]['attendance_detail'] = $attendance_detail;
                }
            }
        }

        return $result;
    }

    public function getAgentStudentData($request, $countOnly = false)
    {

        $collegeId = $request->user()->college_id;
        $post = ($request->input()) ? $request->input() : [];
        $customFilterParts = (isset($post['filter']) && isset($post['filter']['filters'])) ? $post['filter']['filters'] : [];
        $customFilterParts = $this->manageFilterSubQueryData($collegeId, $customFilterParts);

        $columnArr = [
            'rs.id',
            'rs.profile_picture',
            DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as student_name"),
            'rs.application_reference_id as application_id',
            DB::raw("GROUP_CONCAT(CONCAT_WS(' - ', rc.course_code, rc.course_name) SEPARATOR ',@@,') AS course_list"),
            // DB::raw("GROUP_CONCAT(CONCAT(rsc.status,'##', rc.course_code, ' - ', rc.course_name) SEPARATOR ',@@,') AS course_list"),
            DB::raw('GROUP_CONCAT(rsc.id) AS rsc_id_list'),
            'ra.agency_name',
            'rs.DOB',
            'rs.USI',
            'rs.email',
            'rsc.status as status',
            DB::raw("CONCAT(rc.course_code,' - ',rc.course_name) as course"),
            'rs.birthplace',
            'rs.current_mobile_phone as contact',
            'rsd.emergency_contact_person as emergency',
            'rsd.emergency_phone',
            DB::raw("(CASE WHEN rs.student_type = 'Offshore' THEN 'Overseas Student' ELSE (CASE WHEN rs.student_type = 'Onshore' THEN 'Overseas Student in Australia' ELSE 'Resident Student' END) END) as stud_type_name"),
            DB::raw("CONCAT_WS(', ', rs.current_street_no, rs.current_street_name, rs.current_city, rs.current_state, rs.current_postcode, country.name) as address"),
            'generated_stud_id as student_id',
            'campus.name as campus',
        ];

        /* This array use for filterable only */
        $columns = [
            // 'student_name'      => DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as student_name"),
            // 'course'            => DB::raw("CONCAT(rc.course_code,' - ',rc.course_name) as course_list"),
            'student_name' => 'rs.first_name as student_name',
            'course' => 'rc.course_code as course_list',
            'student_type' => 'rs.student_type',
            'DOB' => 'rs.DOB',
            'student_id' => 'generated_stud_id as student_id',
            'campus' => 'campus.name as campus',
            'application_id' => 'rs.application_reference_id as application_id',
            'status' => 'rsc.status as status',
        ];

        $query = Student::alias('rto_students as rs')
            ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rs.id')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->leftjoin('rto_campus as campus', 'campus.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_country as country', 'country.id', '=', 'rs.current_country')
            ->where(['rs.college_id' => $collegeId, 'rsc.agent_id' => $request->agent_id])
            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);

        foreach ($customFilterParts as $filter) {
            if ($filter['field'] == 'extra' && isset($filter['value'])) {
                /* grid wise custom filter apply here */
                $query->where(function ($childQuery) use ($filter) {
                    foreach ($filter['value'] as $fieldName => $fieldValue) {
                        if (count($fieldValue) > 0) {
                            if ($fieldName == 'status') {
                                $childQuery->whereIn('rsc.status', $fieldValue);
                            }
                        }
                    }
                });
            }
        }

        $this->gridDataSorting($query, $post);

        $query->groupBy('rsc.id');

        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function manageFilterSubQueryData($collegeId, $postArr)
    {
        foreach ($postArr as $k => $filter) {
            if ($filter['field'] == 'extra' && isset($filter['value'])) {

                $tempStudentTypeArr = [];

                if (isset($filter['value']['batch']) && count($filter['value']['batch']) > 0) {
                    $result = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                        ->leftjoin('rto_timetable as rt', function ($join) {
                            $join->on('rt.subject_id', '=', 'rsse.subject_id');
                            $join->on('rt.batch', '=', 'rsse.batch');
                        })
                        ->Join('rto_student_courses as rsc', function ($join) {
                            $join->on('rsc.student_id', '=', 'rsse.student_id');
                            $join->on('rsc.course_id', '=', 'rsse.course_id');
                        })
                        ->join('rto_students as rs', 'rs.id', '=', 'rsse.student_id')
                        ->whereIn('rt.batch', $filter['value']['batch'])
                        ->select('rsse.student_id')
                        ->groupBy('rs.id')
                        ->get()->toArray();

                    $studentIdArr = array_column($result, 'student_id');
                    $postArr[$k]['value']['batch_students'] = $studentIdArr;
                }
                unset($postArr[$k]['value']['batch']);

                if (isset($filter['value']['teacher']) && count($filter['value']['teacher']) > 0) {
                    $batchArr = Timetable::Where(['college_id' => $collegeId])
                        ->whereIn('teacher_id', $filter['value']['teacher'])
                        ->groupBy('batch')
                        ->get(['batch'])
                        ->toArray();
                    $batchIdArr = array_column($batchArr, 'batch');

                    $result = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                        ->Join('rto_student_courses as rsc', function ($join) {
                            $join->on('rsc.student_id', '=', 'rsse.student_id');
                            $join->on('rsc.course_id', '=', 'rsse.course_id');
                        })
                        ->join('rto_students as rs', 'rs.id', '=', 'rsse.student_id')
                        ->whereIn('rsse.batch', $batchIdArr)
                        ->select('rsse.student_id')
                        ->groupBy('rs.id')
                        ->get()->toArray();
                    $studentIdArr = array_column($result, 'student_id');
                    $postArr[$k]['value']['teacher_students'] = $studentIdArr;
                }
                unset($postArr[$k]['value']['teacher']);
            }
        }

        return $postArr;
    }

    public function getSidebarResult($request)
    {

        $collegeId = $request->user()->college_id;
        $categoryId = $request->input('id');

        if ($categoryId == 1) {
            $result = [];
            $studType = ['Offshore' => 'Overseas Student', 'Onshore' => 'Overseas Student in Australia', 'Domestic' => 'Resident Student'];

            foreach ($studType as $typeKey => $typeVal) {
                $resultArr = Student::alias('rto_students as rs')
                    ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rs.id')
                    ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
                    ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
                    ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
                    ->leftjoin('rto_country as country', 'country.id', '=', 'rs.current_country')
                    ->where(['rs.college_id' => $collegeId, 'is_student' => 1, 'rs.student_type' => $typeKey])
                    ->orderBy('rc.course_name', 'ASC')
                    ->groupBy('rc.course_code')
                    ->get(['rc.id', 'rc.course_code', 'rc.course_name']);

                $result[] = [
                    'id' => 0,
                    'category_id' => $categoryId,
                    'type' => 'switch',
                    'hasChild' => false,
                    'field' => 'student_type',
                    'value' => $typeKey,
                    'original' => $typeVal,
                    'subtext' => $typeVal,
                ];
                $result[] = [
                    'id' => 0,
                    'category_id' => $categoryId,
                    'type' => 'input',
                    'hasChild' => false,
                    'field' => 'course',
                    'value' => $typeKey,
                    'original' => '',
                    'subtext' => 'Course',
                ];
                foreach ($resultArr as $row) {
                    if ($row->id > 0) {
                        $subText = (strlen($row->course_name) > 25) ? (substr($row->course_name, 0, 25).'...') : $row->course_name;
                        $result[] = [
                            'id' => $row->id,
                            'category_id' => strtolower($typeKey),
                            'type' => 'checkbox',
                            'cssClass' => $typeKey,
                            'hasChild' => false,
                            'field' => strtolower($typeKey).'_course',
                            'value' => $row->id,
                            'original' => $row->course_name,
                            'subtext' => $subText,
                        ];
                    }
                }
            }

            return $result;
        }

        if ($categoryId == 2) {

            $courseArr = Courses::where(['college_id' => $collegeId])->get(['id', 'course_name as text']);
            // $courseArr = Courses::where(['college_id' => $collegeId])->get(['id', DB::raw("CONCAT(course_code,': ',course_name) as text")]);
            // $intakeDateList = CoursesIntakeDate::where(['college_id' => $collegeId])->get(['id', 'intake_name  as text']);
            $filterKey = [
                ['id' => 'between', 'text' => 'Between'],
                ['id' => 'after',   'text' => 'After'],
                ['id' => 'before',  'text' => 'Before'],
            ];
            $result[] = [
                'id' => 0,
                'category_id' => $categoryId,
                'type' => 'dropdown',
                'hasChild' => false,
                'field' => 'Course',
                'value' => 'course_list',
                'arr' => $courseArr,
            ];
            $result[] = [
                'id' => 0,
                'category_id' => $categoryId,
                'type' => 'dropdown',
                'hasChild' => false,
                'field' => 'filterKey',
                'value' => 'filterKey_list',
                'arr' => $filterKey,
            ];
            $result[] = [
                'id' => 0,
                'category_id' => $categoryId,
                'type' => 'dropdown',
                'hasChild' => false,
                'field' => 'intake_date',
                'value' => 'intake_start_list',
                'arr' => [['id' => '', 'text' => 'Select Intake From']],
            ];
            $result[] = [
                'id' => 0,
                'category_id' => $categoryId,
                'type' => 'dropdown',
                'hasChild' => false,
                'field' => 'intake_date',
                'value' => 'intake_end_list',
                'arr' => [['id' => '', 'text' => 'Select Intake To']],
            ];
            $result[] = [
                'id' => 0,
                'category_id' => $categoryId,
                'type' => 'button',
                'hasChild' => false,
                'field' => '',
                'value' => 'add_course',
                'subtext' => 'Add Course',
            ];

            return $result;
        }

        /*if($categoryId == 3){
            $countryArr = Country::limit(10)->get();

            $result = [];
            $result[] = [
                'id' => 0,
                'category_id' => $categoryId,
                'type' => 'input',
                'hasChild' => false,
                'field' => 'Country',
                'value' => '',
                'original' => '',
                'subtext' => ""
            ];
            foreach ($countryArr as $country){
                if(!empty($country->name)){
                    $result[] = [
                        'id' => $country->id,
                        'category_id' => $categoryId,
                        'type' => 'checkbox',
                        'hasChild' => false,
                        'field' => 'type',
                        'value' => $country->id,
                        'original' => $country->id,
                        'subtext' => $country->name
                    ];
                }
            }
            return $result;
        }*/
        if ($categoryId == 3) {
            $query = Student::alias('rto_students as rs')
                ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rs.id')
                ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
                ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
                ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
                ->leftjoin('rto_country as country', 'country.id', '=', 'rs.current_country')
                ->where(['rs.college_id' => $collegeId, 'is_student' => 1]);
            $query->select(['country.id', 'country.name'])   // TODO::GNG-2136 (not apply here)
                ->orderBy('country.name', 'ASC')             // TODO::GNG-2136 (not apply here)
                ->groupBy('country.id');

            $resultArr = $query->get();
            $result = [];
            $result[] = [
                'id' => 0,
                'category_id' => $categoryId,
                'type' => 'input',
                'hasChild' => false,
                'field' => 'Country',
                'value' => 'country',
                'original' => '',
                'subtext' => '',
            ];
            foreach ($resultArr as $row) {
                if ($row->id > 0) {
                    $result[] = [
                        'id' => $row->id,
                        'category_id' => $categoryId,
                        'type' => 'checkbox',
                        'hasChild' => false,
                        'field' => 'country',
                        'value' => $row->id,
                        'original' => $row->name,
                        'subtext' => $row->name,
                    ];
                }
            }

            return $result;
        }

        if ($categoryId == 4) {
            // $studentStatusArr = Config::get('constants.studentStatusArr');
            $studentStatusArr = Config::get('constants.arrCourseStatus');
            $result = [];
            foreach ($studentStatusArr as $key => $status) {
                if (empty($key)) {
                    continue;
                }
                $result[] = [
                    'id' => $key,
                    'category_id' => $categoryId,
                    'type' => 'checkbox',
                    'hasChild' => false,
                    'field' => 'status',
                    'value' => $status,
                    'original' => $status,
                    'subtext' => $status,
                ];
            }

            return $result;
        }

        if ($categoryId == 5) {
            $batchArr = Timetable::where(['college_id' => $collegeId])->groupBy('batch')->get(['id', 'batch as name']);
            $result = [];
            $result[] = [
                'id' => 0,
                'category_id' => $categoryId,
                'type' => 'input',
                'hasChild' => false,
                'field' => 'batch_id',
                'value' => 'batch',
                'original' => '',
                'subtext' => '',
            ];
            foreach ($batchArr as $batch) {
                $result[] = [
                    'id' => $batch->id,
                    'category_id' => $categoryId,
                    'type' => 'checkbox',
                    'hasChild' => false,
                    'field' => 'batch',
                    'value' => $batch->name,
                    'original' => $batch->name,
                    'subtext' => $batch->name,
                ];
            }

            return $result;
        }

        if ($categoryId == 6) {
            $whereArr = ['college_id' => $collegeId, 'staff_type' => 'Staff-Teacher'];
            $teacherArr = Staff::where($whereArr)->get(['id', DB::raw("CONCAT(first_name,' ',last_name) as name")]);
            $result = [];
            $result[] = [
                'id' => 0,
                'category_id' => $categoryId,
                'type' => 'input',
                'hasChild' => false,
                'field' => 'teacher_id',
                'value' => 'teacher',
                'original' => '',
                'subtext' => '',
            ];
            foreach ($teacherArr as $teacher) {
                $result[] = [
                    'id' => $teacher->id,
                    'category_id' => $categoryId,
                    'type' => 'checkbox',
                    'hasChild' => false,
                    'field' => 'teacher',
                    'value' => $teacher->id,
                    'original' => $teacher->name,
                    'subtext' => $teacher->name,
                ];
            }

            return $result;
        }
    }

    public function getSidebarResultNew($request)
    {

        /* filterType = {
        1: "#course_id",
        2: "#student_type",
        3: "#nationality",
        4: "#status",
        5: "#batch",
        6: "#teacher",
        7,8,9 is already occupied
        10: "#campus_filter"
    }; */

        $collegeId = $request->user()->college_id;
        $categoryId = $request->input('id');

        if ($categoryId == 1) {
            return Courses::where([
                'college_id' => $collegeId,
                'activated_now' => Courses::ACTIVITED_YES,
            ])
                ->select(['id as value', DB::raw("CONCAT(course_code,' - ',course_name) as label")])
                    // ->orderBy('value', 'ASC')
                ->orderBy('course_code', 'ASC')
                ->get()
                ->toArray();
        }

        if ($categoryId == 2) {
            $result = [];
            $arrStudentOrigin = Config::get('constants.arrStudentOrigin');
            foreach ($arrStudentOrigin as $key => $status) {
                $result[] = [
                    'value' => $key,
                    'label' => $key,
                ];
            }

            return $result;
        }

        if ($categoryId == 3) {

            return Student::alias('rto_students as rs')
                ->leftjoin('rto_country as country', 'country.id', '=', 'rs.current_country')
                ->where('rs.college_id', $collegeId)
                ->where('country.nationality', '!=', '')
                ->select(['country.id as value', 'country.name as label'])
                ->orderBy('country.nationality', 'ASC')
                ->groupBy('country.id')
                ->get()
                ->toArray();
        }

        if ($categoryId == 4) {
            $studentStatusArr = Config::get('constants.arrCourseStatus');
            $result = [];
            foreach ($studentStatusArr as $key => $status) {
                if (! empty($key)) {
                    $result[] = [
                        'value' => $status,
                        'label' => $status,
                    ];
                }
            }

            return $result;
        }

        if ($categoryId == 5) {
            return Timetable::where(['college_id' => $collegeId])->groupBy('batch')->select(['batch as label', 'batch as value'])->orderBy('value', 'ASC')->get()->toArray();
        }

        if ($categoryId == 6) {
            $whereArr = ['college_id' => $collegeId, 'position' => Staff::POSITION_TEACHER];
            $data = Staff::where($whereArr)->select(['id as value', DB::raw("CONCAT(first_name,' ',last_name) as label"), 'is_active'])->orderBy('label', 'ASC')
                ->get()
                ->toArray();

            return array_map(function ($item) {
                $item['label'] = ($item['is_active'] == 0) ? "<span class='bg-gray-200 text-gray-800 py-1 px-2 rounded text-xs mr-1'> Disabled</span>".$item['label'] : $item['label'];
                $item['encoded'] = false;

                return $item;
            }, $data);
        }

        if ($categoryId == 7) {
            $filterKey = [
                ['value' => 'new',     'text' => 'Exact'],
                ['value' => 'between', 'text' => 'Between'],
                ['value' => 'after',   'text' => 'After'],
                ['value' => 'before',  'text' => 'Before'],
            ];

            return $filterKey;
        }

        if ($categoryId == 8) {
            return CoursesIntakeDate::where(['college_id' => $collegeId])->select(['intake_year as value', 'intake_year as text'])->groupBy('intake_year')->orderBy('intake_year', 'DESC')->get()->toArray();
        }

        if ($categoryId == 9) {
            $intakeDateArr = CoursesIntakeDate::where(['college_id' => $collegeId]);
            // if ($request['filter_list'] == 'between') {
            //     $intakeDateArr->whereBetween('intake_year', [$request['intake_year'], $request['intake_year_2']]);
            // } else if ($request['filter_list'] == 'after') {
            //     $intakeDateArr->where('intake_year', $request['intake_year']);
            // } else if ($request['filter_list'] == 'before') {
            //     $intakeDateArr->where('intake_year', $request['intake_year']);
            // } else if ($request['filter_list'] == 'new') {
            $intakeDateArr->where('intake_year', $request['intake_year']);
            // }
            $result = $intakeDateArr
                ->select([
                    'id as value',
                    DB::raw("CONCAT(intake_name, ' (', DATE_FORMAT(intake_start, '%d-%m-%Y'), ')') as text"),
                ])
                ->groupBy('intake_start', 'intake_name') // Ensure intake_name is included in the groupBy
                ->orderBy('intake_year', 'ASC')
                ->get()
                ->toArray();

            return $result;
        }

        /* 10 = campus list */
        if ($categoryId == 10) {
            return CollegeCampus::selectRaw('id, name')->active()->get()->map(function ($item) {
                return ['value' => $item->id, 'label' => $item->name];
            })->toArray();
        }

        if ($categoryId == 11) {
            // return CourseType::where(['status' => 1, 'college_id' => $collegeId])->get(['id as value', 'title as label']);
            return courseType::Where(['status' => 1])->whereIn('college_id', [Auth::user()->college_id, 0])->get(['id as value', 'title as label']);
        }
    }

    public function getIntakesByFilter($request)
    {

        // $collegeId = $request->user()->college_id;
        // // $query = CoursesIntakeDate::where(['college_id' => $collegeId]);
        // // $query->where('intake_name', $request['intake_date']);

        // // $courseByIntakes = $query->select(['id', 'course_id', 'intake_start', 'intake_name as text'])->groupBy('text')->get()->toArray();
        // // return $courseByIntakes;
        return CoursesIntakeDate::where(['college_id' => $request->user()->college_id, 'id' => $request['intake_date']])->select(['id', 'course_id', 'intake_start', DB::raw("CONCAT(intake_name, ' (', DATE_FORMAT(intake_start, '%d-%m-%Y'), ')') as text")])->groupBy('text')->get()->toArray();
    }

    public function mailAttachment($request, $viewFlag = false)
    {
        $emailAttachments = $request->file('attachment_file');

        $filePath = Config::get('constants.uploadFilePath.TempMailAttachment');
        $destinationPath = Helpers::changeRootPath($filePath);

        $savedFileName = [];
        $mailFileName = [];
        $mailLogFileName = [];
        $counts = 0;
        $imageGet = 0;

        foreach ($emailAttachments as $emailAttachment) {
            $originalName = $emailAttachment->getClientOriginalName();
            $filename = date('YmdHsi').'-'.$originalName;
            $savedFileName[] = $filename;
            if ($imageGet == 0) {
                if (! is_dir($destinationPath['default'])) {
                    File::makeDirectory($destinationPath['default'], 0777, true, true);
                }
                $emailAttachment->move($destinationPath['default'], $filename);
                $mailFileName[] = $destinationPath['default'].$savedFileName[$counts];
                if ($viewFlag) {
                    $mailLogFileName[$originalName] = $destinationPath['view'].$savedFileName[$counts];
                }
                $counts++;
            }
        }
        if ($viewFlag) {
            return [$mailFileName, $mailLogFileName];
        }

        return $mailFileName;
    }

    public function addSMSDetails($request, $arrResult)
    {
        $logData = [
            'college_id' => $request->user()->college_id,
            'comment_by' => $request->user()->id,
            'student_id' => $arrResult['id'],
            'student_course_id' => (isset($arrResult['student_course_id']) ? $arrResult['student_course_id'] : null),
            'today_date' => date('l,d F Y'),
            'type' => '126',
            'log_type' => 'sms',
            'status' => '115',
            'log' => $arrResult['message'],
            'activity_log' => 'sms',
            'view_by' => '',
            'visiblity' => 1,
            'created_by' => $request->user()->id,
            'updated_by' => $request->user()->id,
        ];

        return $this->model->create($logData);
    }

    public function getContent($studentId, $courseId, $content)
    {

        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);

        $objStudentCourse = new StudentCourses;
        $arrStudentCourse = $objStudentCourse->getStudentCoursesEmailContent($studentId, $courseId);

        $arrStudentEnrolledCourse = $objStudentCourse->getArrayStudentEnrolledCourseName($studentId);

        $arrStudentOfferedCourse = $objStudentCourse->getArrayStudentOfferedCourseName($studentId);
        if (! empty($arrStudentCourse)) {
            $row = $arrStudentCourse[0];
            // $domain = env('APP_URL');
            $domain = url('/');

            // if($type == 'preview'){
            $basePath = $destinationPath['view'];
            // }else{
            // $basePath = $destinationPath['default'];
            // }

            $usePublicImages = $row['allow_public_images'] ?? true;
            $college_logo_url = $this->getUploadedFileUrl($basePath.$row['college_logo'], $usePublicImages);
            $college_signature_url = $this->getUploadedFileUrl($basePath.$row['college_signature'], $usePublicImages);
            $dean_signature_url = $this->getUploadedFileUrl($basePath.$row['dean_signature'], $usePublicImages);

            $college_logo = '<img src="'.$college_logo_url.'" alt="College Logo" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $college_signature = '<img src="'.$college_signature_url.'" alt="College Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $dean_signature = '<img src="'.$dean_signature_url.'" alt="Dean/CEO Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';

            $enrolledCourseList = '';
            if (! empty($arrStudentEnrolledCourse)) {
                $enrolledCourseList = '<ul>';
                foreach ($arrStudentEnrolledCourse as $value) {
                    $enrolledCourseList .= '<li>'.$value['course_code'].' : '.$value['course_name'].' ('.date('d-m-Y', strtotime($value['start_date'])).' - '.date('d-m-Y', strtotime($value['finish_date'])).')</li>';
                }
                $enrolledCourseList .= '</ul>';
            }

            $offeredCourseList = '';
            if (! empty($arrStudentOfferedCourse)) {
                $offeredCourseList = '<ul>';
                foreach ($arrStudentOfferedCourse as $value) {
                    $offeredCourseList .= '<li>'.$value['course_code'].' : '.$value['course_name'].' ('.date('d-m-Y', strtotime($value['start_date'])).' - '.date('d-m-Y', strtotime($value['finish_date'])).')</li>';
                }
                $offeredCourseList .= '</ul>';
            }

            $dataArr = [
                '{AlterEmail1}' => $row['emergency_email'],
                '{AlterEmail2}' => $row['emergency_email'],
                //  "{CollegeEmail}"        => $row['coe_name'],
                '{CollegeLogo}' => $college_logo,
                '{CollegeEmail}' => $row['college_email'],
                '{Country}' => $row['country_name'],
                '{CountryBirth}' => $row['birth_country'],
                // "{CurrentDate}" => date('d-m-Y'),
                '{CurrentDate}' => $this->getCurrentDateTimeWithTimeZone($row['college_timezone'], 'd-m-Y'), // TODO::GN-2333
                '{DoB}' => date('d-m-Y', strtotime($row['birth_date'])),
                '{DOB}' => date('d-m-Y', strtotime($row['birth_date'])),
                '{DoB Without Stroke}' => '******************',
                '{Email}' => $row['student_email'],
                '{ExpDate}' => date('d-m-Y', strtotime($row['visa_expiry_date'])),
                '{Fax}' => $row['fax'],
                '{StudentId}' => $row['generated_stud_id'],
                '{FirstName}' => $row['first_name'],
                '{MiddleName}' => $row['middel_name'],
                '{LastName}' => $row['family_name'],
                '{Gender}' => $row['gender'],
                '{Mobile}' => $row['current_mobile_phone'],
                '{Nationality}' => $row['nationality'],
                '{NickName}' => $row['nickname'],
                '{PassportNo}' => $row['passport_no'],
                '{Phone}' => $row['current_mobile_phone'],
                '{Postcode}' => $row['current_postcode'],
                '{State}' => $row['current_state'],
                '{StreetAddress}' => $row['current_street_name'],
                '{StreetNumber}' => $row['current_street_no'],
                '{UnitDetail}' => $row['current_unit_detail'],
                '{BuildingName}' => $row['current_building_name'],
                '{Suburb}' => $row['current_city'],
                '{Title}' => $row['name_title'],
                '{UserName}' => $row['generated_stud_id'],
                '{VisaType}' => $row['visa_type'],
                '{CourseCode}' => $row['course_code'],
                '{CourseName}' => $row['course_name'],
                '{CollegeRtoCode}' => $row['RTO_code'],
                '{CollegeCircosCode}' => $row['CRICOS_code'],
                '{CollegeLegalName}' => $row['legal_name'],
                '{CollegeName}' => $row['entity_name'],
                '{CollegeSignature}' => $college_signature,
                '{DeanName}' => $row['dean_name'],
                '{DeanSignature}' => $dean_signature,
                '{CollegeContactPerson}' => $row['contact_person'],
                '{CollegeContactPhone}' => $row['contact_phone'],
                '{CollegeURL}' => $row['college_url'],
                '{CollegeABN}' => $row['college_ABN'],
                '{CollegeFax}' => $row['fax'],
                '{CourseType}' => $row['course_type'],
                '{Campus}' => $row['campus_name'],
                '{StudentType}' => $row['student_type'],
                '{TeacherFirstName}' => $row['teacher_first_name'],
                '{TeacherLastName}' => $row['teacher_last_name'],
                '{TeacherEmail}' => $row['teacher_email'],
                '{TeacherMobile}' => $row['teacher_mobile'],
                '{AgencyName}' => $row['agency_name'],
                '{AgentName}' => $row['agent_name'],
                '{AgentEmail}' => $row['agent_email'],
                '{AgentTelephone}' => $row['agent_telephone'],
                '{EnrolledCourseList}' => $enrolledCourseList,
                '{OfferedCourseList}' => $offeredCourseList,

            ];

            foreach ($dataArr as $key => $value) {
                $content = str_replace("$key", $value, $content);
            }

            return $content;
        } else {
            return false;
        }
        // return $student;
    }

    public function getContentWithSubject($studentId, $courseId, $content, $subject = '')
    {

        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);

        $objStudentCourse = new StudentCourses;
        $arrStudentCourse = $objStudentCourse->getStudentCoursesEmailContent($studentId, $courseId);

        $arrStudentEnrolledCourse = $objStudentCourse->getArrayStudentEnrolledCourseName($studentId);

        $arrStudentOfferedCourse = $objStudentCourse->getArrayStudentOfferedCourseName($studentId);
        if (! empty($arrStudentCourse)) {
            $row = $arrStudentCourse[0];
            // $domain = env('APP_URL');
            $domain = url('/');

            // if($type == 'preview'){
            $basePath = $destinationPath['view'];
            // }else{
            // $basePath = $destinationPath['default'];
            // }

            $usePublicImages = $row['allow_public_images'] ?? true;
            $college_logo_url = $this->getUploadedFileUrl($basePath.$row['college_logo'], $usePublicImages);
            $college_signature_url = $this->getUploadedFileUrl($basePath.$row['college_signature'], $usePublicImages);
            $dean_signature_url = $this->getUploadedFileUrl($basePath.$row['dean_signature'], $usePublicImages);

            $college_logo = '<img src="'.$college_logo_url.'" alt="College Logo" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $college_signature = '<img src="'.$college_signature_url.'" alt="College Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $dean_signature = '<img src="'.$dean_signature_url.'" alt="Dean/CEO Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';

            $enrolledCourseList = '';
            if (! empty($arrStudentEnrolledCourse)) {
                $enrolledCourseList = '<ul>';
                foreach ($arrStudentEnrolledCourse as $value) {
                    $enrolledCourseList .= '<li>'.$value['course_code'].' : '.$value['course_name'].' ('.date('d-m-Y', strtotime($value['start_date'])).' - '.date('d-m-Y', strtotime($value['finish_date'])).')</li>';
                }
                $enrolledCourseList .= '</ul>';
            }

            $offeredCourseList = '';
            if (! empty($arrStudentOfferedCourse)) {
                $offeredCourseList = '<ul>';
                foreach ($arrStudentOfferedCourse as $value) {
                    $offeredCourseList .= '<li>'.$value['course_code'].' : '.$value['course_name'].' ('.date('d-m-Y', strtotime($value['start_date'])).' - '.date('d-m-Y', strtotime($value['finish_date'])).')</li>';
                }
                $offeredCourseList .= '</ul>';
            }

            $dataArr = [
                // "{Current Date}" => date('d-m-Y'),
                '{Current Date}' => $this->getCurrentDateTimeWithTimeZone($row['college_timezone'], 'd-m-Y'), // TODO::GN-2333
                '{AlterEmail1}' => $row['emergency_email'],
                '{AlterEmail2}' => $row['emergency_email'],
                //  "{CollegeEmail}"        => $row['coe_name'],
                '{CollegeLogo}' => $college_logo,
                '{CollegeEmail}' => $row['college_email'],
                '{Country}' => $row['country_name'],
                '{CountryBirth}' => $row['birth_country'],
                // "{CurrentDate}" => date('d-m-Y'),
                '{CurrentDate}' => $this->getCurrentDateTimeWithTimeZone($row['college_timezone'], 'd-m-Y'), // TODO::GN-2333
                '{DoB}' => date('d-m-Y', strtotime($row['birth_date'])),
                '{DOB}' => date('d-m-Y', strtotime($row['birth_date'])),
                '{DoB Without Stroke}' => '******************',
                '{Email}' => $row['student_email'],
                '{ExpDate}' => date('d-m-Y', strtotime($row['visa_expiry_date'])),
                '{Fax}' => $row['fax'],
                '{Student ID}' => $row['generated_stud_id'],
                '{Student Name}' => $row['first_name'].' '.$row['family_name'],
                '{StudentId}' => $row['generated_stud_id'],
                '{FirstName}' => $row['first_name'],
                '{MiddleName}' => $row['middel_name'],
                '{LastName}' => $row['family_name'],
                '{Gender}' => $row['gender'],
                '{Mobile}' => $row['current_mobile_phone'],
                '{Nationality}' => $row['nationality'],
                '{NickName}' => $row['nickname'],
                '{PassportNo}' => $row['passport_no'],
                '{Phone}' => $row['current_mobile_phone'],
                '{Postcode}' => $row['current_postcode'],
                '{State}' => $row['current_state'],
                '{StreetAddress}' => $row['current_street_name'],
                '{StreetNumber}' => $row['current_street_no'],
                '{UnitDetail}' => $row['current_unit_detail'],
                '{BuildingName}' => $row['current_building_name'],
                '{Suburb}' => $row['current_city'],
                '{Title}' => $row['name_title'],
                '{UserName}' => $row['generated_stud_id'],
                '{VisaType}' => $row['visa_type'],
                '{CourseCode}' => $row['course_code'],
                '{CourseName}' => $row['course_name'],
                '{CollegeRtoCode}' => $row['RTO_code'],
                '{CollegeCircosCode}' => $row['CRICOS_code'],
                '{CollegeLegalName}' => $row['legal_name'],
                '{CollegeName}' => $row['entity_name'],
                '{CollegeSignature}' => $college_signature,
                '{DeanName}' => $row['dean_name'],
                '{DeanSignature}' => $dean_signature,
                '{CollegeContactPerson}' => $row['contact_person'],
                '{CollegeContactPhone}' => $row['contact_phone'],
                '{CollegeURL}' => $row['college_url'],
                '{CollegeABN}' => $row['college_ABN'],
                '{CollegeFax}' => $row['fax'],
                '{CourseType}' => $row['course_type'],
                '{Campus}' => $row['campus_name'],
                '{StudentType}' => $row['student_type'],
                '{TeacherFirstName}' => $row['teacher_first_name'],
                '{TeacherLastName}' => $row['teacher_last_name'],
                '{TeacherEmail}' => $row['teacher_email'],
                '{TeacherMobile}' => $row['teacher_mobile'],
                '{AgencyName}' => $row['agency_name'],
                '{AgentName}' => $row['agent_name'],
                '{AgentEmail}' => $row['agent_email'],
                '{AgentTelephone}' => $row['agent_telephone'],
                '{EnrolledCourseList}' => $enrolledCourseList,
                '{OfferedCourseList}' => $offeredCourseList,
            ];

            foreach ($dataArr as $key => $value) {
                $content = str_replace("$key", $value, $content);
                $subject = str_replace("$key", $value, $subject);
            }

            return [$subject, $content];
        } else {
            return false;
        }
        // return $student;
    }

    /* Start Student Profile Module */
    public function getVisaStatusData($request)
    {
        return VisaStatus::Where(strtolower($request->student_type), 1)->select('id as Id', 'name as Name')->get()->toArray();
    }

    public function getActivityLog($request)
    {
        $columnArr = [
            'rsc.*',
            'user.name as user_name',
            'rss1.value as type',
            'rss2.value as status',
            DB::raw("CONCAT(MONTHNAME(rsc.created_at),' , ',year(rsc.created_at)) as activity_month"),
        ];
        $whereArr = [
            'rsc.college_id' => $request->college_id,
            'rsc.student_id' => $request->student_id,
            // 'rsc.log_type' => 'profile'
        ];

        return StudentCommunicationLog::from('rto_student_communication as rsc')
            ->leftjoin('rto_users as user', 'user.id', '=', 'rsc.updated_by')
            ->leftjoin('rto_setup_section as rss1', 'rss1.id', '=', 'rsc.type')
            ->leftjoin('rto_setup_section as rss2', 'rss2.id', '=', 'rsc.status')
            ->where($whereArr)
            ->select($columnArr)
            ->orderBy('rsc.created_at', 'DESC')
            ->get()
            ->toArray();
    }

    public function saveCommunicationLog($request, $studentId, $replacedContent, $emailTo, $emailFrom, $attachmentLogData = [], $mailData = [])
    {
        $userId = isset($request->user_id) ? $request->user_id : $request->user()->id;
        $collegeId = isset($request->college_id) ? $request->college_id : $request->user()->college_id;

        $todayDate = date('l,d F Y');
        $log = 'Send to : '.$emailTo.'<br>';
        if (isset($request->reply_to_email) && ! empty($request->reply_to_email)) {
            $log .= 'Reply-To : '.$request->reply_to_email.'<br>';
        }
        if (isset($request->email_cc) && ! empty($request->email_cc)) {
            $log .= 'CC : '.$request->email_cc.'<br>';
        }
        if (isset($request->email_bcc) && ! empty($request->email_bcc)) {
            $log .= 'BCC : '.$request->email_bcc.'<br>';
        }
        $log .= 'From email : '.$emailFrom.'<br>';
        $log .= 'Subject : '.$mailData['subject'].'<br>';
        $log .= $replacedContent;

        if (count($attachmentLogData) > 0) {
            $attachmentList = [];
            foreach ($attachmentLogData as $name => $path) {
                $path = UploadService::preview($path);
                $attachmentList[] = "<a class='font-medium text-blue-600 dark:text-blue-500 hover:underline' href='$path' target='_blank' title='$name'>$name</a>";
            }
            $log .= 'Attachments : '.implode(', ', $attachmentList);
        }

        if (! empty($request->offer_comm_log)) {
            $mergedArray = [
                'today_date' => $todayDate,
                'college_id' => $collegeId,
                'student_id' => $studentId,
                'type' => 26,
                'log_type' => $request->log_type,
                'status' => 16,
                'log' => $log,
                'created_by' => $userId,
                'updated_by' => $userId,
            ];

            if ($request instanceof Request) {
                $finalArray = array_merge($request->input(), $mergedArray);
            } else {
                $finalArray = $request->request->add($mergedArray);
            }

            $objStudCommunication = $this->model->create($finalArray);
        }
    }

    public function saveStaffCommunicationLog($request, $staffId, $replacedContent, $emailTo, $emailFrom, $attachmentLogData = [], $mailData = [])
    {
        $userId = isset($request->user_id) ? $request->user_id : $request->user()->id;
        $collegeId = isset($request->college_id) ? $request->college_id : $request->user()->college_id;

        $todayDate = date('l,d F Y');
        $log = 'Send to : '.$emailTo.'<br>';
        if (isset($request->reply_to_email) && ! empty($request->reply_to_email)) {
            $log .= 'Reply-To : '.$request->reply_to_email.'<br>';
        }
        if (isset($request->email_cc) && ! empty($request->email_cc)) {
            $log .= 'CC : '.$request->email_cc.'<br>';
        }
        if (isset($request->email_bcc) && ! empty($request->email_bcc)) {
            $log .= 'BCC : '.$request->email_bcc.'<br>';
        }
        $log .= 'From email : '.$emailFrom.'<br>';
        $log .= 'Subject : '.$mailData['subject'].'<br>';
        $log .= $replacedContent;

        if (count($attachmentLogData) > 0) {
            $attachmentList = [];
            foreach ($attachmentLogData as $name => $path) {
                $attachmentList[] = "<a class='font-medium text-blue-600 dark:text-blue-500 hover:underline' href='$path' target='_blank' title='$name'>$name</a>";
            }
            $log .= 'Attachments : '.implode(', ', $attachmentList);
        }

        $sectionId = StaffCommunicationLog::SECTION_ID;     // 5 for Diary-Staff as per DB:rto_setup_section_option
        $typeId = StaffCommunicationLog::TYPE_ID;           // 6 for type_name="Type" as per DB:rto_setup_section_type
        $statusId = StaffCommunicationLog::STATUS_ID;       // 7 for type_name="Status" as per DB:rto_setup_section_type
        $type_name = StaffCommunicationLog::TYPE_NAME;      // as per DB:rto_default_setup_section
        $status_name = StaffCommunicationLog::STATUS_NAME;  // as per DB:rto_default_setup_section

        $defaultType = SetupSection::getSetupSectionId($collegeId, $sectionId, $typeId, $type_name);           // replace by 11
        $defaultStatus = SetupSection::getSetupSectionId($collegeId, $sectionId, $statusId, $status_name);     // replace by 12
        // End Default Status & Type/Category ID

        StaffCommunicationLog::create([
            'college_id' => $collegeId,
            'today_date' => now()->format('l, d F Y'),
            'staff_id' => $staffId,
            'type' => $request->input('type', $defaultType),
            'status' => $request->input('status', $defaultStatus),
            'log' => $log,
            'created_by' => $userId,
            'updated_by' => $userId,
        ]);

    }

    public function saveWarningEmailLog($request, $studentId, $replacedContent, $emailTo, $emailFrom, $attachmentLogData = [])
    {
        if ($request instanceof Request) {
            $userId = isset($request->user_id) ? $request->user_id : $request->user()->id;
            $collegeId = isset($request->college_id) ? $request->college_id : $request->user()->college_id;
        } else {
            $userId = $request->user_id ?? 0;
            $collegeId = $request->college_id ?? 0;
        }
        $todayDate = date('l,d F Y');
        $log = 'Send to : '.$emailTo.'<br>';
        if (isset($request->reply_to_email) && ! empty($request->reply_to_email)) {
            $log .= 'Reply-To : '.$request->reply_to_email.'<br>';
        }
        if (isset($request->email_cc) && ! empty($request->email_cc)) {
            $log .= 'CC : '.$request->email_cc.'<br>';
        }
        if (isset($request->email_bcc) && ! empty($request->email_bcc)) {
            $log .= 'BCC : '.$request->email_bcc.'<br>';
        }
        $log .= 'From email : '.$emailFrom.'<br>';
        $log .= 'Subject : '.$request->email_subject.'<br>';
        $log .= $replacedContent;

        if (count($attachmentLogData) > 0) {
            $attachmentList = [];
            foreach ($attachmentLogData as $name => $path) {
                $attachmentList[] = "<a href='$path' class='font-medium text-blue-600 dark:text-blue-500 hover:underline' target='_blank'>$name</a>";
            }
            $log .= 'Attachments : '.implode(', ', $attachmentList);
        }
        $objStudCommunication = null;
        if ($request->is_attendance_warning == '1') {
            if ($request instanceof Request) {
                $request->request->add([
                    'today_date' => $todayDate,
                    'college_id' => $collegeId,
                    'student_id' => $studentId,
                    'log_type' => 'warning',
                    'activity_log' => 'email',
                    'log' => $log,
                    'created_by' => $userId,
                    'updated_by' => $userId,
                ]);
                $insertData = $request->input();
            } else {
                $insertData = [
                    'today_date' => $todayDate,
                    'college_id' => $collegeId,
                    'student_id' => $studentId,
                    'log_type' => 'warning',
                    'activity_log' => 'email',
                    'log' => $log,
                    'created_by' => $userId,
                    'updated_by' => $userId,
                ];
                $request = (array) $request;
                $insertData = [...$request, ...$insertData];
            }
            $objStudCommunication = $this->model->create($insertData);
        }

        return $objStudCommunication;
    }

    public function getStudentListForDropDown($request)
    {

        $whereArr = ['college_id' => Auth::user()->college_id, 'is_student' => 1];
        $selectArr = [
            'id',
            'id as value',
            'profile_picture as profile_pic',
            DB::raw("CONCAT(first_name,' ',family_name) as name"),
            'current_mobile_phone as contact',
            'generated_stud_id as username',
            'email',
        ];
        $resArr = Student::where($whereArr)->select($selectArr)->get()->toArray();

        foreach ($resArr as $key => $student) {
            $resArr[$key]['name'] = trim($student['name']);
            $resArr[$key]['profile_pic'] = $this->getStudentProfilePicPath($student['id'], $student['profile_pic'], 'small');
        }

        return $resArr;
    }

    public function getCourseProgressTimeline($request)
    {

        $course_detail = StudentCourses::from('rto_student_courses as t1')
            ->leftjoin('rto_courses as t2', 't2.id', '=', 't1.course_id')
            ->where('t1.id', $request->student_course_id)
            ->select('t1.id', 't2.college_id', 't1.student_id', 't1.course_id')
            ->get()->first();

        $whereArr = [
            'a1.college_id' => $course_detail['college_id'],
            'a1.student_id' => $course_detail['student_id'],
            'a1.course_id' => $course_detail['course_id'],
        ];
        $course_progress = StudentSubjectEnrolment::from('rto_student_subject_enrolment as a1')
            ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'a1.unit_id')
            ->where($whereArr)
            ->select(
                'a1.id',
                'a1.unit_id',
                'a1.final_outcome',
                'rsu.unit_name',
                'rsu.unit_code',
                'a1.activity_start_date',
                'a1.activity_finish_date'
            )
            ->orderBy('a1.activity_start_date', 'DESC')
            ->get()->toArray();

        return $course_progress;
    }

    public function getStudentDetail($studentId)
    {

        $arrRoleType = Config::get('constants.arrRoleType');
        $studentRoleType = array_search('Student', $arrRoleType);

        return Student::alias('rto_students as rs')
            ->join('rto_colleges as rc', 'rc.id', '=', 'rs.college_id')
            ->join('rto_student_details as rsd', 'rsd.student_id', '=', 'rs.id')
            ->leftjoin('rto_users', function ($join) use ($studentRoleType) {
                $join->on('rto_users.username', '=', 'rs.generated_stud_id');
                $join->on('rto_users.role_id', '=', DB::raw($studentRoleType));
            })
            ->leftjoin('rto_student_courses as rsc', 'rs.id', '=', 'rsc.student_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_country as sc', 'sc.id', '=', 'rs.current_country')
            ->leftjoin('rto_country as ac', 'ac.id', '=', 'ra.office_country')
            ->leftjoin('rto_setup_section as rss1', 'rss1.id', '=', 'rs.visa_status')
            ->leftjoin('rto_setup_section as rss2', 'rss2.id', '=', 'rsd.EPL_test_name')
            ->where('rs.id', $studentId)
            ->select([
                'rc.college_logo',
                'rs.*',
                'rto_users.username',
                'rsd.picup_fee',
                'rsd.service_fee',
                'rsd.OSHC_fee',
                'rsd.account_manager_id',
                'rsd.EPL_overall_score',
                'ra.agency_name',
                'ra.office_address as agent_address',
                'ra.office_city as agent_city',
                'ra.office_state as agent_state',
                'ra.office_postcode as agent_postcode',
                'ac.name as agent_country',
                'sc.nationality',
                'sc.nationality as stud_nationality',
                'sc.name as country_name',
                'rss1.value as visaStatus',
                'rss2.value as EPL_test_name',
            ])
            ->get()
            ->first();
    }
    /* End Student Profile Module */

    /* Start default added attendance entry & manage fail job related to attendance module */
    public function manageEnrollStudentAttendance($studentId, $batchArr)
    {
        $timetableData = TimetableDetail::from('rto_timetable_detail as rtd')
            ->join('rto_timetable as rt', 'rt.id', '=', 'rtd.timetable_id')
            ->whereIn('rt.batch', $batchArr)
            ->get([
                'rtd.id',
                'rtd.timetable_date',
                'rtd.day',
                'rt.id as timetable_id',
                'rt.attendance_type',
                'rt.semester_id',
                'rt.term',
                'rt.batch',
            ])
            ->toArray();
        // dd($timetableData);
        if (count($timetableData) > 0) {
            $this->removeExistingLogData($studentId, $batchArr);
        }

        // $added = 0;
        $flag = '';

        foreach ($timetableData as $row) {
            $dataArr = [
                'timetable_id' => $row['timetable_id'],
                'student_id' => $studentId,
                'attendance_day' => $row['day'],
                'attendance_date' => $row['timetable_date'],
                'attendance_type' => $row['attendance_type'],
            ];
            $count = StudentAttendance::where($dataArr)->get()->count();

            if ($count == 0) {
                $dataArr['week_period'] = null;
                $dataArr['total_hours'] = 0;
                $dataArr['status'] = 'absent';

                DB::beginTransaction();
                try {
                    // $res = StudentAttendance::create($dataArr);

                    $objStudentAttendance = new StudentAttendance;
                    $objStudentAttendance->timetable_detail_id = $row['id'];
                    $objStudentAttendance->timetable_id = $row['timetable_id'];
                    $objStudentAttendance->student_id = $studentId;
                    $objStudentAttendance->attendance_day = $row['day'];
                    $objStudentAttendance->attendance_date = $row['timetable_date'];
                    $objStudentAttendance->attendance_type = $row['attendance_type'];
                    $objStudentAttendance->week_period = null;
                    $objStudentAttendance->total_hours = 0;
                    // $objStudentAttendance->status          = 'absent';
                    $res = $objStudentAttendance->save();

                    DB::commit();
                    if ($res) {
                        $flag = true;
                        // $this->addLogData($studentId, $row, 'success', 'success');
                    }
                    // $added++;
                    // return $res;
                } catch (\Exception $e) {
                    DB::rollBack();
                    $this->addLogData($studentId, $row, 'fail', $e->getMessage());
                    $flag = false;
                    break;
                    // safeDD($e);
                    // throw new ApplicationException($e->getMessage());
                }
            }
        }

        return $flag;
    }

    public function getFailAttendanceData($request, $countOnly = false)
    {
        $post = ($request->input()) ? $request->input() : [];
        $columnArr = [
            't1.id',
            'rs.id as student_id',
            DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as student_name"),
            't1.batch',
            't1.created_at as failed_at',
            DB::raw("DATE_FORMAT(t1.created_at, '%d %b %Y') as formatted_failed_date"),
            't1.log',
        ];

        $columns = [
            'id' => 't1.id',
            'student_name' => DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as student_name"),
            'batch' => 't1.batch',
            'failed_at' => DB::raw("DATE_FORMAT(t1.created_at, '%d %b %Y') as formatted_failed_date"),
        ];

        $query = DB::table('attendance_log_manage as t1')
            ->join('rto_students as rs', 'rs.id', '=', 't1.student_id')
            ->where('t1.status', 'fail')
            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);

        $this->gridDataSorting($query, $post);

        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function addLogData($studentId, $data, $status, $logData)
    {
        /*$dataArr = [
            'student_id'    => $studentId,
            'timetable_id'  => $data['timetable_id'],
            'semester_id'   => $data['semester_id'],
            'term'          => $data['term'],
            'batch'         => $data['batch'],
            'status'        => $status,
            'log'           => $logData,
        ];*/

        DB::beginTransaction();
        try {
            // $res = AttendanceLog::create($dataArr);

            $objAttendanceLog = new AttendanceLog;
            $objAttendanceLog->student_id = $studentId;
            $objAttendanceLog->timetable_id = $data['timetable_id'];
            $objAttendanceLog->semester_id = $data['semester_id'];
            $objAttendanceLog->term = $data['term'];
            $objAttendanceLog->batch = $data['batch'];
            $objAttendanceLog->status = $status;
            $objAttendanceLog->log = $logData;
            $res = $objAttendanceLog->save();

            DB::commit();

            return $res;
        } catch (\Exception $e) {
            DB::rollBack();
            // safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function removeExistingLogData($studentId, $batchArr)
    {
        DB::beginTransaction();
        try {
            $res = AttendanceLog::where('student_id', $studentId)->whereIn('batch', $batchArr)->delete();
            DB::commit();

            return $res;
        } catch (\Exception $e) {
            DB::rollBack();

            return false;
            throw new ApplicationException($e->getMessage());
        }
    }
    /* End default added attendance entry & manage fail job related to attendance module */

    public function getStudentDetails($studentId)
    {
        return Student::leftjoin('rto_student_details as rsd', 'rto_students.id', '=', 'rsd.student_id')
            ->leftjoin('rto_country as country', 'country.id', '=', 'rto_students.nationality')
            ->leftjoin('rto_english_test as ret', 'rsd.EPL_test_name', '=', 'ret.id')
            ->leftjoin('rto_colleges', 'rto_colleges.id', '=', 'rto_students.college_id')
            ->leftjoin('rto_employment_status as res', 'rsd.current_employment_status', '=', 'res.id')
            ->leftjoin('rto_student_courses as rsc', 'rto_students.id', '=', 'rsc.student_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_campus as rc', 'rc.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_courses as cour', 'cour.id', '=', 'rsc.course_id')
            ->leftjoin('rto_setup_section as rvc', 'rvc.id', '=', 'rto_students.visa_status')
            ->leftjoin('rto_country', 'rto_country.id', '=', 'rto_students.current_country')
            ->select('rto_students.*', 'rto_colleges.college_logo', 'rsd.*', 'ret.name as english_name', 'rsc.issued_date as issued_date', 'cour.course_name as course_name', 'rsc.start_date as start_date', 'rsc.finish_date as finish_date', 'rsc.enroll_fee as enroll_fee', 'rsc.course_id as course_id', 'rsc.course_fee as course_fee', 'rsc.course_upfront_fee as course_upfront_fee', 'rsc.course_material_fee as course_material_fee', 'rsc.offer_id as offer_id', 'ra.agency_name', 'rc.name as campus_name', 'rvc.value as visa_type', 'rto_country.nationality as stud_nationality', 'rto_country.name as country_name', 'rto_colleges.college_name', 'country.nationality as nationality_of_student')
            ->where('rto_students.id', '=', $studentId)
            ->get();
    }

    public function getSelectedCountry($country)
    {
        return Country::where('id', '=', $country)->get(['name']);
    }

    public $form = [
        'keyword' => '',
        'courses' => [],
        'student_type' => [],
        'status' => [],
        'origin' => [],
        'batches' => [],
        'teachers' => [],
        'intake_dates' => [],
        'nationality' => [],
        'course_type' => [],
        'sort_by' => '',
        'sort_direction' => 'asc',

    ];

    protected $students;

    public function getStudentSearchData($request, $countOnly = false)
    {

        $this->form = [
            'keyword' => ! empty($request->input('searchText')) ? $request->input('searchText') : '',
            'courses' => ! empty($request->input('course_id')) ? $request->input('course_id') : [],
            'campuses' => ! empty($request->input('campus_id')) ? $request->input('campus_id') : [],
            'student_type' => ! empty($request->input('student_type')) ? $request->input('student_type') : [],
            'status' => ! empty($request->input('status')) ? $request->input('status') : [],
            'origin' => [],
            'batches' => ! empty($request->input('batch')) ? $request->input('batch') : [],
            'teachers' => ! empty($request->input('teacher')) ? $request->input('teacher') : [],
            'intake_dates' => ! empty($request->input('student_intake')) ? $request->input('student_intake') : [],
            'nationality' => ! empty($request->input('nationality')) ? $request->input('nationality') : [],
            'course_type' => ! empty($request->input('course_type')) ? $request->input('course_type') : [],
            'sort_by' => ! empty($request->input('sort')) ? $request->input('sort')[0]['field'] : 'courses.created_at',
            'sort_direction' => ! empty($request->input('sort')) ? $request->input('sort')[0]['dir'] : 'desc',
        ];
        $collegeId = $request->user()->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $appliedFilter = false;
        $studentRoleType = Roles::TYPE_STUDENT;
        // dd($this->form);
        $searchKeyword = $this->form['keyword'];
        if (strlen($searchKeyword) < 3) {
            $searchKeyword = '';
        }

        $courseTypeIds = array_map('intval', $this->form['course_type']);

        $hasFilters = collect($this->form)
            ->except(['keyword', 'sort_by', 'sort_direction'])
            ->filter(fn ($val) => ! empty($val))
            ->isNotEmpty()
                        || ! empty($searchKeyword);

        // Use Scout search when we have complex filters or search keyword
        // Use direct database query only when we have ONLY course_type filter
        $useScoutSearch = $hasFilters && (! empty($searchKeyword) || ! empty($this->form['courses']) || ! empty($this->form['status']) || ! empty($this->form['campuses']) || ! empty($this->form['student_type']) || ! empty($this->form['batches']) || ! empty($this->form['teachers']) || ! empty($this->form['intake_dates']) || ! empty($this->form['nationality']));

        if ($useScoutSearch) {
            $scoutBuilder = Student::search($searchKeyword, function (Indexes $searchEngine, string $query, array $options) {
                $options['matchingStrategy'] = 'all';
                if ( /* $filterCriteria  == 'all' && */ isset($options['filter'])) {
                    $options['filter'] = '('.$options['filter'].')';
                }

                // $options['filter'] = 'is_student=1 AND ((courses.course.id IN [7] AND courses.status = "Cancelled") OR (courses.course.id IN [7] AND courses.status = "Completed"))';

                // is_student=1 AND courses.course.id IN [7] AND courses.status IN ["Cancelled", "Completed"])
                // $options['filter'] = ["is_student = 1", ]

                // $options['filter'] = 'is_student=1 AND (courses.course.id = 7 OR courses.status  = "Cancelled")';
                return $searchEngine->search($query, $options);
            });

            ScoutFilterService::FilterStudentForList($scoutBuilder, $this->form);

            $scoutBuilder->where('is_student', 1);
            $studentsFound = $scoutBuilder->paginate(25);
            $studentIds = $studentsFound->pluck('id')->toArray();

            // Apply course_type filter post-Scout if needed
            // This creates an AND condition with Scout results
            if (count($courseTypeIds) > 0) {
                $studentIds = Student::whereIn('id', $studentIds)
                    ->whereHas('studentCourses', function ($query) use ($courseTypeIds) {
                        $query->whereIn('course_type_id', $courseTypeIds);

                        // If course_id filter was also applied in Scout, ensure consistency
                        if (! empty($this->form['courses'])) {
                            $query->whereIn('course_id', $this->form['courses']);
                        }
                    })
                    ->pluck('id')
                    ->toArray();
            }
            // dump("Scout");
        } else {
            $query = Student::select('rto_students.id', DB::raw('MAX(rto_student_courses.created_at) as latest_course_created_at'))
                ->join('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_students.id')
                ->where('rto_students.is_student', 1)
                ->when(count($courseTypeIds) > 0, function ($query) use ($courseTypeIds) {
                    $query->whereIn('rto_student_courses.course_type_id', $courseTypeIds);
                });

            $studentsFound = $query->groupBy('rto_students.id')
                        // ->orderByDesc('latest_course_created_at')
                ->paginate(25);

            $studentIds = $studentsFound->pluck('id')->toArray();
            // dump("Normal");
        }
        // dump($studentIds);
        $today = date('Y-m-d');
        // Do NOT overwrite $studentIds here; keep any post-Scout filtering applied above
        $results = Student::select(
            'rto_students.id',
            'ru.username',
            'ru.last_login',
            // 'rto_students.status as galaxy_status',
            'ru.status as galaxy_status',
            'generated_stud_id',
            'generated_stud_id as student_id',
            'rto_students.DOB',
            'rto_students.profile_picture',
            DB::raw("TRIM(CONCAT_WS(' ', rto_students.first_name, rto_students.family_name)) as first_name"),
            DB::raw("TRIM(CONCAT_WS(' ', rto_students.first_name, rto_students.family_name)) as student_name"),
            'rto_students.student_type',
            DB::raw('COUNT(rsa.attendance_date) as total_day'),
            DB::raw("SUM(rsa.status = 'absent' AND rsa.attendance_date < '$today') as absent_day"),
            DB::raw("SUM(rsa.status = 'present' AND rsa.attendance_date < '$today') as present_day"),
            DB::raw("SUM(rsa.status = 'absent' AND rsa.attendance_date > '$today') as pending_day"),
            'campus.name as campus',
            'ra.agency_name',
            'rto_students.USI',
            'rto_students.email',
            'rto_students.birthplace',
            'rto_students.current_mobile_phone as contact',
            'rsd.emergency_contact_person as emergency',
            'rsd.emergency_phone',
            DB::raw("CONCAT_WS(', ', rto_students.current_street_no, rto_students.current_street_name, rto_students.current_city, rto_students.current_state, rto_students.current_postcode, country.name) as address"),
            DB::raw("(CASE WHEN rto_students.student_type = 'Offshore' THEN 'Overseas Student' ELSE (CASE WHEN rto_students.student_type = 'Onshore' THEN 'Overseas Student in Australia' ELSE 'Resident Student' END) END) as stud_type_name"),
        )
            ->leftjoin('rto_users as ru', 'ru.username', '=', 'rto_students.generated_stud_id')
            ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rto_students.id')
            ->leftJoin('rto_student_attendance as rsa', 'rsa.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_campus as campus', 'campus.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_country as country', 'country.id', '=', 'rto_students.current_country')
            ->groupBy('rto_students.id')
            ->whereIn('rto_students.id', $studentIds)
            ->where('rto_students.is_student', 1)
            ->when(count($courseTypeIds) > 0, function ($query) use ($courseTypeIds) {
                $query->whereHas('studentCourses', function ($q) use ($courseTypeIds) {
                    $q->whereIn('course_type_id', $courseTypeIds);
                });
            })
            ->with([
                'studentCourses' => function ($q) {
                    $courses = ! empty($this->form['courses']) ? $this->form['courses'] : null;
                    $statusFilters = ! empty($this->form['status']) ? $this->form['status'] : null;
                    $prioritySortQuery = generateOrderByCondition('rto_student_courses.course_id', $courses);
                    $sortDataQuery = generateStatusFilterConditions($statusFilters);
                    $q = $q
                        ->leftjoin('rto_campus as campus', 'campus.id', '=', 'rto_student_courses.campus_id')
                        ->with(['course', 'course.courseSubjects.unit'])
                        ->select(
                            '*',
                            'rto_student_courses.status as status',
                            'campus.name as currentCampus',
                            DB::raw('(CASE WHEN (finish_date > now() AND start_date > now()) THEN 0 WHEN finish_date > now() THEN '.dbRawL10('DATEDIFF(now(), start_date)').' ELSE '.dbRawL10('DATEDIFF(finish_date, start_date)').' END) as days'),
                            DB::raw('DATEDIFF(finish_date, start_date) as diff_days'),
                            DB::raw('('.$sortDataQuery.') as ordertext'),
                        );
                    if ($prioritySortQuery) {
                        $q->orderBy(DB::raw($prioritySortQuery));
                    }
                    $q->orderBy(DB::raw('
                                ('.$sortDataQuery.')'), 'ASC');

                    return $q;

                },
                'initialPayments' => function ($q) {
                    return $q->leftJoin('rto_student_initial_payment_details as a2', 'a2.student_course_id', '=', 'rto_student_initial_payment.student_course_id')
                        ->leftjoin('rto_student_courses as rsc', 'a2.student_course_id', '=', 'rsc.id')
                        ->select(
                            'rto_student_initial_payment.student_id',
                            'rto_student_initial_payment.course_id',
                            DB::raw('rto_student_initial_payment.tution_fee as total'),
                            DB::raw('(SUM(CASE When a2.payment_status != "unpaid" THEN a2.upfront_fee_pay ELSE 0 END)) as paid'),
                            // DB::raw('(SUM(CASE When a2.payment_status != "paid" AND a2.due_date > now() THEN (a2.upfront_fee_to_pay - a2.upfront_fee_pay) ELSE 0 END)) as unpaid'),
                            DB::raw('(rsc.course_fee - SUM(a2.upfront_fee_pay)) as unpaid'),
                            DB::raw('(SUM(CASE When a2.payment_status != "paid" AND a2.due_date < now() THEN (a2.upfront_fee_to_pay - a2.upfront_fee_pay) ELSE 0 END)) as missed'),
                            DB::raw('(SUM(CASE When a2.payment_status = "paid" THEN 1 ELSE 0 END)) as paid_count'),
                            DB::raw('(SUM(CASE When a2.payment_status != "paid" AND a2.due_date > now() THEN 1 ELSE 0 END)) as unpaid_count'),
                            DB::raw('(SUM(CASE When a2.payment_status != "paid" AND a2.due_date < now() THEN 1 ELSE 0 END)) as missed_count')
                        )
                        ->groupBy('rto_student_initial_payment.student_id')
                        ->groupBy('rto_student_initial_payment.id');
                },
            ])
            ->get();
        $enrollments = $this->getFirstEnrollments($studentIds);
        // Manually load enrollments
        $results->each(function (&$student) use ($enrollments) {
            $studentId = $student->id ?? null;
            $enrollment = collect([]);
            if ($studentId && isset($enrollments[$studentId])) {
                $enrollment = $enrollments[$studentId];
            }
            $student->setRelation('enrollments', $enrollment);
        });
        // Compute total correctly based on the final filtered IDs and query path
        $filtersApplied = collect($this->form)->filter(function ($item, $key) {
            return ! in_array($key, ['sort_by', 'sort_direction']) && ($item || (is_array($item) && count($item) > 0));
        })->count() > 0;

        if ($filtersApplied || ! empty($searchKeyword)) {
            if ($useScoutSearch) {
                // If course_type was applied as a post-Scout filter, total must reflect the intersection
                if (count($courseTypeIds) > 0) {
                    // We only have the current page of Scout results; reflect the intersection count for this page
                    // This prevents inflated totals that ignore other filters
                    $total = count($studentIds);
                } else {
                    $total = $studentsFound->total() ?? 0;
                }
            } else {
                // Direct DB path already includes course_type filter in the base query
                $total = $studentsFound->total() ?? 0;
            }
        } else {
            // No filters: show total students (optionally constrained by course_type if provided)
            $total = Student::whereNotNull('generated_stud_id')
                ->where('rto_students.is_student', 1)
                ->when(count($courseTypeIds) > 0, function ($query) use ($courseTypeIds) {
                    $query->whereHas('studentCourses', function ($q) use ($courseTypeIds) {
                        $q->whereIn('course_type_id', $courseTypeIds);
                    });
                })
                ->whereHas('studentCourses')
                ->count();
        }
        $records = $results->map(function ($data) {
            $data->secure_id = encryptIt($data->id);
            // $enrollments = collect($data['enrollments']);
            // $initialPayments = collect($data['initial_payments']);
            $enrollments = $data->enrollments ?? collect([]);
            $initialPayments = $data->initialPayments ?? collect([]);
            $studentCourses = $data->studentCourses ?? collect([]);
            unset($data->studentCourses);
            // unset($data->enrollments);
            unset($data->initialPayments);
            $student_courses = $studentCourses->map(function ($item, $index) use ($enrollments, $initialPayments) {
                if ($index == 0) {
                    $course_process = $enrollments->filter(function ($enroll) use ($item) {
                        return isset($enroll->course_id) && isset($item->course_id) && $enroll->course_id == $item->course_id;
                    });
                    // $item->course_process = array_values($course_process);
                    $item->course_process = $course_process;

                    $initial_payments = $initialPayments->filter(function ($pay) use ($item) {
                        return isset($pay->course_id) && isset($item->course_id) && $pay->course_id == $item->course_id;
                    });
                    // $item->initial_payments = array_values($initial_payments);
                    $item->initial_payments = $initial_payments;
                }

                return $item;
            });

            // $student_courses = collect($student_courses);
            $data->allCampusName = $student_courses->pluck('name')->unique()->values()->all();
            $data->student_courses = $student_courses;
            $data->initial_payments = $student_courses->first()->initial_payments ?? [];
            $data->currentCampus = $student_courses->first()->currentCampus ?? '';
            $data->profile_pic = $this->getStudentProfilePicPath($data->id, $data->profile_picture);
            $data->mini_profile_pic = $this->getStudentProfilePicPath($data->id, $data->profile_picture, 'small');
            $data = $data->toArray();

            return $data;
        });

        return compact('records', 'total');
    }

    public function getFirstEnrollments($studentIds = null)
    {
        $today = date('Y-m-d');
        $statusFilters = ! empty($this->form['status']) ? $this->form['status'] : null;
        $courses = ! empty($this->form['courses']) ? $this->form['courses'] : null;
        $courses = ($courses) ? array_map('intval', $courses) : null;
        $sortDataQuery = generateStatusFilterConditions($statusFilters);
        $subquery = Student::join('rto_student_subject_enrolment', 'rto_student_subject_enrolment.student_id', '=', 'rto_students.id')
            ->join('rto_student_courses', function ($join) {
                $join->on('rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id')
                    ->on('rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id');
            })
            ->leftJoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rto_student_subject_enrolment.unit_id')
            ->select(
                'rto_student_courses.id AS studentcourse_id',
                'rto_student_subject_enrolment.id',
                'rto_student_subject_enrolment.course_id',
                'rto_student_subject_enrolment.student_id',
                'rto_student_subject_enrolment.unit_id',
                'rto_student_subject_enrolment.final_outcome',
                'rsu.unit_name as unit_title',
                DB::raw("CONCAT(rsu.vet_unit_code, ' : ', rsu.unit_name) as unit_name"),
                DB::raw('(CASE
                                    WHEN rto_student_subject_enrolment.final_outcome = "C" THEN "green-500"
                                    WHEN rto_student_subject_enrolment.final_outcome = "NYC" THEN "red-500"
                                    ELSE "gray-200"
                                END) as color'),
                DB::raw('(CASE
                                    WHEN rto_student_subject_enrolment.activity_start_date <= "'.$today.'"
                                        AND rto_student_subject_enrolment.activity_finish_date >= "'.$today.'" THEN 1
                                    ELSE 0
                                END) as is_active'),
                DB::raw('(CASE WHEN rto_student_courses.status = "Current Student" THEN "a1" WHEN rto_student_courses.status = "Enrolled" THEN "a2" ELSE rto_student_courses.status END) AS status_text'),
                DB::raw('DENSE_RANK() OVER (PARTITION BY rto_student_subject_enrolment.student_id
                                ORDER BY '.$sortDataQuery.' ASC) AS row_num')
            )
            ->whereIn('rto_students.id', $studentIds)
            ->orderBy(DB::raw('(CASE WHEN rto_student_courses.status = "Current Student" THEN "a1" WHEN rto_student_courses.status = "Enrolled" THEN "a2" ELSE rto_student_courses.status END)'), 'ASC');
        if ($courses) {
            $subquery->whereIn('rto_student_courses.course_id', $courses);
        }
        // Get the SQL and bindings from the subquery
        $sql = $subquery->toSql();
        // Execute the final query using raw query builder and apply bindings
        $data = DB::table(DB::raw("({$sql}) as ordered_courses"))
            ->mergeBindings($subquery->getQuery()) // Apply bindings from the subquery
            ->select(
                'studentcourse_id',
                'id',
                'course_id',
                'student_id',
                'unit_id',
                'final_outcome',
                'unit_title',
                'unit_name',
                'color',
                'is_active',
                'status_text',
                'row_num'
            )
            ->where('row_num', 1)
            ->get()
            ->groupBy('student_id');

        return $data;
    }

    public function getStudentMoreDetailsV2($result, $collegeId)
    {

        foreach ($result['data'] as $k => $row) {

            $result['data'][$k]['profile_pic'] = $this->getStudentProfilePicPath($row['id'], $row['profile_picture']);
            $result['data'][$k]['mini_profile_pic'] = $this->getStudentProfilePicPath($row['id'], $row['profile_picture'], 'small');

            foreach ($row['student_courses'] as $k1 => $course) {
                if ($k1 == 0) {
                    $whereArr = [
                        'a1.college_id' => $collegeId,
                        'a1.student_id' => $course['student_id'],
                        'a1.course_id' => $course['course_id'],
                    ];
                    $whereArr2 = [
                        'a1.college_id' => $collegeId,
                        'a1.course_id' => $course['course_id'],
                    ];
                    $current_course = StudentSubjectEnrolment::from('rto_student_subject_enrolment as a1')
                        ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'a1.unit_id')
                        ->where($whereArr)
                        ->select(
                            DB::raw('(SUM(CASE WHEN a1.final_outcome != "" THEN 1 ELSE 0 END)) as total_unit'),
                            DB::raw('(SUM(CASE WHEN a1.final_outcome = "C" OR a1.final_outcome = "NYC" THEN 1 ELSE 0 END)) as use_unit'),
                            DB::raw('CONCAT(SUM(CASE WHEN a1.final_outcome = "C" THEN 1 ELSE 0 END), " C, ", SUM(CASE WHEN a1.final_outcome = "NYC" THEN 1 ELSE 0 END), " NYC") as title')
                        )
                        ->groupBy('a1.course_id')
                        ->get()
                        ->toArray();

                    if (count($current_course) == 0) {
                        $current_course = CourseSubject::from('rto_course_subject as a1')
                            ->join('rto_subject_unit as rsu', 'rsu.subject_id', '=', 'a1.subject_id')
                            ->where($whereArr2)
                            ->select(
                                DB::raw('(COUNT(a1.id)) as total_unit'),
                                DB::raw('(CASE WHEN a1.id > 0 THEN 0 ELSE 0 END) as use_unit'),
                                DB::raw('(CASE WHEN a1.id > 0 THEN "0 C, 0 NYC" ELSE "0 C, 0 NYC" END) as title')
                            )
                            ->get()
                            ->toArray();
                        if (count($current_course) == 0) {
                            $current_course = [['title' => '0 C, 0 NYC', 'total_unit' => 0, 'use_unit' => 0]];
                        }
                    }
                    $row['student_courses'][$k1]['current_course'] = $current_course;

                    $attendanceResult = $this->getStudentAttendanceData($whereArr);

                    // For Attendance Column
                    $row['student_courses'][$k1]['course_attendance'] = $attendanceResult['course_attendance'];

                    // For Attendance details only for mini profile
                    $row['student_courses'][$k1]['course_attendance'] = $attendanceResult['calculated_attendance'];
                }
            }

            $result['data'][$k]['student_courses'] = $row['student_courses'];
        }

        return $result['data'];
    }

    public function searchStudentByName($searchText = '', $countOnly = false)
    {
        $this->form = [
            'keyword' => $searchText || '',
        ];

        $collegeId = auth()->user()->college_id;
        $userId = auth()->user()->id;
        $appliedFilter = false;
        $studentRoleType = Roles::TYPE_STUDENT;
        $userIsAgent = auth()->user()->isAgent();
        if ($userIsAgent) {
            $agentId = Agent::where('college_id', $collegeId)->where('user_id', $userId)->value('id');
            $studentsFound = Student::select('rto_students.*')->join('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_students.id')
                ->Join('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
                ->where([
                    'rto_agents.college_id' => $collegeId,
                    'rto_agents.id' => $agentId,
                ])
                ->where(function ($query) use ($searchText) {
                    $query->where(DB::raw("CONCAT_WS(' ', first_name, middel_name, family_name)"), 'LIKE', "%$searchText%")
                        ->orWhere('generated_stud_id', 'LIKE', "%$searchText%")
                        ->orWhere('application_reference_id', 'LIKE', "%$searchText%");
                })
                ->groupBy('rto_students.id')
                ->paginate(25);
        } else {
            $scoutBuilder = Student::search($searchText, function (Indexes $searchEngine, string $query, array $options) {
                $options['matchingStrategy'] = 'all';
                if (isset($options['filter'])) {
                    $options['filter'] = '('.$options['filter'].')';
                }

                return $searchEngine->search($query, $options);
            });
            // ScoutFilterService::FilterStudentForList($scoutBuilder);
            $scoutBuilder->where('is_student', 1);
            $studentsFound = $scoutBuilder->paginate(25);
        }

        return $studentsFound;
    }

    public function getAgentStudents($collegeId, $agentId)
    {
        return Student::from('rto_students')
            ->join('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_students.id')
            ->Join('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->where([
                'rto_agents.college_id' => $collegeId,
                'rto_agents.id' => $agentId,
            ])
            ->select([
                'rto_students.id',
                'rto_students.generated_stud_id',
                'rto_students.first_name',
                'rto_students.family_name',
                'rto_students.student_type',
                'rto_agents.agency_name',
            ])
            ->groupBy('rto_students.id')
            ->get();
    }

    public function getFilteredStudentIds(
        string $keyword = '',
        array $courseTypeIds = [],
        array $courseIds = [],
        array $campusIds = [],
        array $studentType = [],
        array $status = [],
        array $origin = [],
        array $batches = [],
        array $teachers = [],
        array $intakeDates = [],
        array $nationality = [],
        string $sortBy = 'courses.created_at',
        string $sortDirection = 'desc'
    ) {
        $this->form = [
            'keyword' => $keyword,
            'course_type' => $courseTypeIds,
            'courses' => $courseIds,
            'campuses' => $campusIds,
            'student_type' => $studentType,
            'status' => $status,
            'origin' => $origin,
            'batches' => $batches,
            'teachers' => $teachers,
            'intake_dates' => $intakeDates,
            'nationality' => $nationality,
            'sort_by' => $sortBy,
            'sort_direction' => $sortDirection,
        ];

        $scoutBuilder = Student::search($this->form['keyword'], function (Indexes $searchEngine, string $query, array $options) {
            $options['matchingStrategy'] = 'all';
            if (isset($options['filter'])) {
                $options['filter'] = "({$options['filter']})";
            }

            return $searchEngine->search($query, $options);
        });

        ScoutFilterService::FilterStudentForList($scoutBuilder, $this->form);
        $scoutBuilder->where('is_student', 1);

        $studentsFound = $scoutBuilder->paginate(100000);

        return $studentsFound->pluck('id')->toArray();
    }

    public function getAllStudentListForDropdownWithSearch($request, $countOnly = false)
    {
        $this->form = [
            'keyword' => ! empty($request->input('searchText')) ? $request->input('searchText') : '',
            'courses' => ! empty($request->input('course_id')) ? $request->input('course_id') : [],
            'campuses' => ! empty($request->input('campus_id')) ? $request->input('campus_id') : [],
            'student_type' => ! empty($request->input('student_type')) ? $request->input('student_type') : [],
            'status' => ! empty($request->input('status')) ? $request->input('status') : [],
            'origin' => [],
            'batches' => ! empty($request->input('batch')) ? $request->input('batch') : [],
            'teachers' => ! empty($request->input('teacher')) ? $request->input('teacher') : [],
            'intake_dates' => ! empty($request->input('student_intake')) ? $request->input('student_intake') : [],
            'nationality' => ! empty($request->input('nationality')) ? $request->input('nationality') : [],
            'course_type' => ! empty($request->input('course_type')) ? $request->input('course_type') : [],
            'sort_by' => ! empty($request->input('sort')) ? $request->input('sort')[0]['field'] : 'courses.created_at',
            'sort_direction' => ! empty($request->input('sort')) ? $request->input('sort')[0]['dir'] : 'desc',

        ];

        $collegeId = $request->user()->college_id;
        $post = ($request->input()) ? $request->input() : [];
        $appliedFilter = false;
        $studentRoleType = Roles::TYPE_STUDENT;
        // dd($this->form);

        $scoutBuilder = Student::search($this->form['keyword'], function (Indexes $searchEngine, string $query, array $options) {
            $options['matchingStrategy'] = 'all';
            if (isset($options['filter'])) {
                $options['filter'] = '('.$options['filter'].')';
            }

            return $searchEngine->search($query, $options);
        });

        ScoutFilterService::FilterStudentForList($scoutBuilder, $this->form);
        $scoutBuilder->where('is_student', 1);
        $today = date('Y-m-d');
        $studentsFound = $scoutBuilder->paginate(100000);
        $studentIds = $studentsFound->pluck('id')->toArray();
        $results = Student::select(
            'rto_students.id',
            'rto_students.id as value',
            'rto_students.profile_picture as profile_pic',
            DB::raw("CONCAT(rto_students.first_name,' ',rto_students.family_name) as name"),
            'rto_students.current_mobile_phone as contact',
            'ru.username',
            'rto_students.email'
        )
            ->leftjoin('rto_users as ru', 'ru.username', '=', 'rto_students.generated_stud_id')
            ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rto_students.id')
            ->leftJoin('rto_student_attendance as rsa', 'rsa.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_campus as campus', 'campus.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_country as country', 'country.id', '=', 'rto_students.current_country')
            ->groupBy('rto_students.id')
            ->whereIn('rto_students.id', $studentIds)
            ->where('rto_students.is_student', 1)
            ->get();
        // Manually load enrollments

        if (collect($this->form)->filter(function ($item, $key) {
            return ! in_array($key, ['sort_by', 'sort_direction']) && ($item || (is_array($item) && count($item) > 0));
        })->count() > 0) {
            $total = $studentsFound->total() ?? 0;
        } else {
            $total = Student::whereNotNull('generated_stud_id')
                ->where('rto_students.is_student', 1)
                ->whereHas('studentCourses')->count();
        }
        $records = $results->map(function ($data) {
            $data = $data->toArray();

            return $data;
        });

        return $records->toArray();
    }

    public function saveStudentCommunicationLog($request, $studentId, $replacedContent, $emailTo, $emailFrom, $attachmentLogData = [], $mailData = [])
    {

        $userId = isset($request->user_id) ? $request->user_id : $request->user()->id;
        $collegeId = isset($request->college_id) ? $request->college_id : $request->user()->college_id;

        $todayDate = date('l,d F Y');
        $log = 'Send to : '.$emailTo.'<br>';
        if (isset($request->reply_to_email) && ! empty($request->reply_to_email)) {
            $log .= 'Reply-To : '.$request->reply_to_email.'<br>';
        }
        if (isset($request->email_cc) && ! empty($request->email_cc)) {
            $log .= 'CC : '.$request->email_cc.'<br>';
        }
        if (isset($request->email_bcc) && ! empty($request->email_bcc)) {
            $log .= 'BCC : '.$request->email_bcc.'<br>';
        }
        $log .= 'From email : '.$emailFrom.'<br>';
        $log .= 'Subject : '.$mailData['subject'].'<br>';
        $log .= $replacedContent;
        if (count($attachmentLogData) > 0) {
            $attachmentList = [];
            foreach ($attachmentLogData as $key => $path) {
                $name = basename($path);
                $attachmentList[] = "<a style='color: rgb(37 99 235 / var(--tw-text-opacity)) !important;' class='font-medium text-blue-600 dark:text-blue-500 hover:underline' href='$path' target='_blank' title='$name'>$name</a>";
            }
            $log .= 'Attachments : '.implode(', ', $attachmentList);
        }
        $mergedArray = [
            'today_date' => $todayDate,
            'college_id' => $collegeId,
            'student_id' => $studentId,
            'type' => $request->type,
            'log_type' => $request->log_type,
            'status' => isset($request->status) ? $request->status : 16,
            'log' => $log,
            'created_by' => $userId,
            'updated_by' => $userId,
        ];

        if ($request instanceof Request) {
            $finalArray = array_merge($request->input(), $mergedArray);
        } else {
            $finalArray = $request->request->add($mergedArray);
        }
        StudentCommunicationLog::create($finalArray);

    }

    public function setLetterBodyContent($studentId, $courseId, $content, $request_from = 'profile')
    {

        // $collegeId = Auth::user()->college_id;
        // Condition is For student list page
        if ($courseId != '' && $request_from == 'profile') {
            $courseId = StudentCourses::where('id', $courseId)->value('course_id');
        }

        [$convertedSubject, $convertedData] = $this->getLetterEmailContentWithSubject($content, $studentId, $courseId);

        return $convertedData;
        // $arrStudentCourse = StudentCourses::getStudentCoursesEmailContent($studentId, $courseId);

        // $arrStudentSubjectEnrollment = StudentSubjectEnrolment::getCourseProgressSummaryByUnitV2($collegeId, $studentId, $courseId);

        // $arrStudentEnrolledCourse = StudentCourses::getArrayStudentEnrolledCourseName($studentId);
        // $arrStudentOfferedCourse = StudentCourses::getArrayStudentOfferedCourseName($studentId);

        // $row = $arrStudentCourse[0];

        // $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        // $destinationPath = Helpers::changeRootPath($filePath);

        // $domain = url('/');
        // $basePath = $destinationPath['view'];
        // $college_logo_url = $domain.str_replace('\\', "/", $basePath).$row['college_logo'];

        // $college_logo = '<img src="'.$college_logo_url.'" alt="college logo" style="height: auto; width: 150px;padding-left: 49px; padding-top: 20px;"  />';

        // $enrolledCourseList = '';
        // if(!empty($arrStudentEnrolledCourse)) {
        //     $enrolledCourseList = '<ul>';
        //     foreach ($arrStudentEnrolledCourse as $value) {
        //       $enrolledCourseList .= '<li>'.$value["course_code"].' : '.$value["course_name"].' ('.date("d-m-Y", strtotime($value["start_date"])).' - '.date("d-m-Y", strtotime($value["finish_date"])).')</li>';
        //     }
        //     $enrolledCourseList .= '</ul>';
        // }

        // $offeredCourseList = '';
        // if(!empty($arrStudentOfferedCourse)) {
        //     $offeredCourseList = '<ul>';
        //     foreach ($arrStudentOfferedCourse as $value) {
        //       $offeredCourseList .= '<li>'.$value["course_code"].' : '.$value["course_name"].' ('.date("d-m-Y", strtotime($value["start_date"])).' - '.date("d-m-Y", strtotime($value["finish_date"])).')</li>';
        //     }
        //     $offeredCourseList .= '</ul>';
        // }

        // $enrolledUnitList = '';
        // if(!empty($arrStudentSubjectEnrollment)) {
        //     $enrolledUnitList = '<ul>';
        //     foreach ($arrStudentSubjectEnrollment as $value) {
        //       $enrolledUnitList .= '<li>'.$value["unit_code"].' : '.$value["unit_name"].'</li>';
        //     }
        //     $enrolledUnitList .= '</ul>';
        // }

        // $dataArr = array(
        //     "{AccountManager}"      => $row['account_manager'],
        //     "{agentid}"             => $row['agent_id'],
        //     "{AgentName}"           => $row['agent_name'],
        //     "{CampusName}"          => $row['campus_name'],
        //     "{CoENo}"               => $row['coe_name'],
        //     "{CollegeEmail}"        => $row['college_email'],
        //     "{Country}"             => $row['country_name'],
        //     "{Course Attd}"         => "*********",
        //     "{CourseAttempt}"       => $row['course_attempt'],
        //     "{CourseCode}"          => $row['course_code'],
        //     "{CourseID}"            => $row['course_code'],
        //     "{CourseName}"          => $row['course_name'],
        //     "{CourseType}"          => $row['course_type'],
        //     "{CricosCode}"          => $row['cricos_code'],
        //     //"{Current Date}"      => date('d/m/Y'),
        //     "{CurrentDate}"        => $this->getCurrentDateTimeWithTimeZone($row['college_timezone'], 'd/m/Y'),
        //     "{Dob}"                 => date('d/m/Y', strtotime($row['birth_date'])),
        //     "{Duration}"            => $row['total_weeks'],
        //     "{DurationType}"        => "Week",
        //     "{Email}"               => $row['student_email'],
        //     "{EntityName}"          => $row['entity_name'],
        //     "{Fax}"                 => $row['fax'],
        //     "{Finishdate}"          => date('d/m/Y', strtotime($row['finish_date'])),
        //     "{FirstName}"           => $row['first_name'],
        //     "{Gender}"              => $row['gender'],
        //     "{LastLetterName}"      => "*********",
        //     "{LastLetterSentDate}"  => "*********",
        //     "{LastName}"            => $row['family_name'],
        //     "{Lastname}"            => $row['family_name'],
        //     "{Mobile}"              => $row['current_mobile_phone'],
        //     "{Name}"                => $row['student_name'],
        //     "{Nationality}"         => $row['nationality'],
        //     "{OfferId}"             => $row['offer_id'],
        //     "{OfferNo}"             => $row['offer_id'],
        //     "{Page Break}"          => "<hr/>",
        //     "{Passportno}"          => $row['passport_no'],
        //     "{Phone}"               => $row['current_mobile_phone'],
        //     "{PostCode}"            => $row['current_postcode'],
        //     "{Proj Attd}"           => "*********",
        //     "{Startdate}"           => date('d/m/Y', strtotime($row['start_date'])),
        //     "{State}"               => $row['current_state'],
        //     "{Status}"              => $row['status'],
        //     "{StreetAddress}"       => $row['current_street_name'],
        //     "{StudentId}"           => $row['generated_stud_id'],
        //     "{Suburb}"              => $row['current_city'],
        //     "{Title}"               => $row['name_title'],
        //     "{StreetNumber}"        => $row['current_street_no'],
        //     "{UnitDetail}"          => $enrolledUnitList,
        //     "{BuildingName}"        => $row['current_building_name'],
        //     "{CollegeRtoCode}"      => $row['RTO_code'],
        //     "{CollegeCircosCode}"   => $row['CRICOS_code'],
        //     "{CollegeLegalName}"    => $row['legal_name'],
        //     "{CollegeName}"         => $row['entity_name'],
        //     "{CollegeContactPerson}"=> $row['contact_person'],
        //     "{CollegeContactPhone}" => $row['contact_phone'],
        //     "{CollegeURL}"          => $row['college_url'],
        //     "{CollegeABN}"          => $row['college_ABN'],
        //     "{CollegeFax}"          => $row['fax'],
        //     "{StudentType}"         => $row['student_type'],
        //     "{TeacherFirstName}"    => $row['teacher_first_name'],
        //     "{TeacherLastName}"     => $row['teacher_last_name'],
        //     "{TeacherEmail}"        => $row['teacher_email'],
        //     "{TeacherMobile}"       => $row['teacher_mobile'],
        //     "{AgencyName}"          => $row['agency_name'],
        //     "{AgentEmail}"          => $row['agent_email'],
        //     "{AgentTelephone}"      => $row['agent_telephone'],
        //     "{CollegeLogo}"         => $college_logo,
        //     "{EnrolledCourseList}"  => $enrolledCourseList,
        //     "{OfferedCourseList}"   => $offeredCourseList,
        //     "{CourseStartDate}"     => (isset($row['start_date'])) ? date('d-m-Y', strtotime($row['start_date'])):'',
        //     "{CourseEndDate}"       => (isset($row['finish_date'])) ? date('d-m-Y', strtotime($row['finish_date'])):'',
        //     "{CourseDuration}"      => (isset($row['total_weeks']))?$row['total_weeks'].' Weeks':'',
        //     "{StudentContactEmail}" => $row['personalEmail'],
        //     "{StudentAlternateEmail}"   => $row['AlternateEmail'],
        //     "{StudentEmergencyEmail}"   => $row['emergency_email'],
        // );

        // foreach($dataArr as $key => $value){
        //     $content = str_replace("$key", $value, $content);
        // }
        // return $content;
    }

    public function getParameterList($content)
    {

        $start = '{#';
        $end = '#}';
        $split_string = explode($end, $content);
        foreach ($split_string as $data) {
            $str_pos = strpos($data, $start);
            $last_pos = strlen($data);
            $capture_len = $last_pos - $str_pos;
            $value = substr($data, $str_pos + 1, $capture_len);
            $result[] = str_replace('#', '', $value);
        }

        if ((count($result) > 1)) {
            array_pop($result);

            return $result;
        } else {
            return [];
        }
    }

    public function isCourseParameter($content)
    {
        $courseParameters = LetterParameter::COURSE_PARAMETER;
        foreach ($courseParameters as $param) {
            if (strpos($content, $param) !== false) {
                return true;
            }
        }

        return false;
    }

    public function assignParameterValue($content, $paramVal)
    {
        foreach ($paramVal as $row) {
            // Handle both {#tag1#} and {tag1} formats
            $pattern1 = '/\{\#\s*'.preg_quote($row['key'], '/').'\s*\#\}/';
            $pattern2 = '/\{\s*'.preg_quote($row['key'], '/').'\s*\}/';
            $value = $row['value'];

            // Replace both formats
            $content = preg_replace($pattern1, $value, $content);
            $content = preg_replace($pattern2, $value, $content);
        }

        return $content;
    }
}
