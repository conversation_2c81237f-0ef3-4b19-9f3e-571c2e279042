<?php

namespace App\Repositories;

use App\Helpers\Helpers;
use App\Model\v2\Agent;
use App\Model\v2\CollegeCampus;
use App\Model\v2\Courses;
use App\Model\v2\CourseTemplate;
use App\Model\v2\EmailTemplateDocuments;
use App\Model\v2\FailedEmails;
use App\Model\v2\ReportLetterFileAttachment;
use App\Model\v2\Student;
use App\Model\v2\StudentCommunicationLog;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentCourseStatusHistory;
use App\Model\v2\StudentDetails;
use App\Model\v2\StudentOffers;
use App\Model\v2\StudentServiceInformation;
use App\Model\v2\StudentSubjectEnrolment;
use App\Model\v2\StudentUnitEnrollment;
use App\Model\v2\StudyReason;
use App\Model\v2\UnitModule;
use App\Model\v2\UserAuditLog;
use App\Traits\CommonTrait;
use App\Traits\CourseSubjectTrait;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class StudentProfileCommonRepository
{
    use CommonTrait;
    use CourseSubjectTrait;

    public function saveActivityNote($logData)
    {
        return StudentCommunicationLog::create($logData);
    }

    public function createCourseStatusHistory($dataArr)
    {
        return StudentCourseStatusHistory::create($dataArr);
    }

    public function updateCourseStatus($data, $studCourseId)
    {
        return StudentCourses::where('id', $studCourseId)->update($data);
    }

    public function getStatusHistoryData($collegeId, $studentCourseId)
    {
        return StudentCourseStatusHistory::from('rto_student_status_change_history as rssch')
             // ->join('rto_student_courses as rsc', function ($join) {
             //     $join->on('rsc.course_id', '=', 'rssch.course_id');
             //     $join->on('rsc.student_id', '=', 'rssch.student_id');
             // })
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rssch.created_by')
            ->join('rto_students as rs', 'rs.id', '=', 'rssch.student_id')
            ->where(['rssch.college_id' => $collegeId, 'rssch.course_id' => $studentCourseId])
            ->select('rssch.*', DB::raw('IFNULL(ru.name, "System") as created_by_name'), DB::raw('CONCAT(rs.first_name," ",rs.family_name) as student_name'))
            ->orderBy('id', 'DESC')
            ->get()
            ->toArray();
    }

    public function getStudentCoursesData($studentId, $color = false, $orderByDate = false)
    {
        $resultQuery = StudentCourses::withStudentDetails($studentId);
        if ($orderByDate) {
            $resultQuery->orderBy('t1.start_date', 'ASC');
        } else {
            $resultQuery->orderBy(DB::raw('(CASE WHEN t1.status = "Current Student" THEN "a1" WHEN t1.status = "Enrolled" THEN "a2" ELSE t1.status END)'), 'ASC');
        }

        $resultArr = $resultQuery->get()->toArray();

        if ($color) {
            foreach ($resultArr as $key => $res) {
                // $resultArr[$key]['bg_color'] = $this->getColorCode($res['status']);
                $resultArr[$key]['bg_color'] = Helpers::getStudCourseColorCode($res['status']);
            }
        }

        return $resultArr;
    }

    public function getOfferIdList($studentId)
    {
        return StudentCourses::where(['student_id' => $studentId])->groupBy('offer_id')->select(['offer_id as Id', 'offer_id as Name'])->get()->toArray();
    }

    public function getCourses()
    {
        $query = Courses::join('rto_course_campus', 'rto_course_campus.course_id', '=', 'rto_courses.id')
            // ->select('rto_courses.id', 'rto_courses.course_name', 'rto_courses.course_code')
            ->where(['rto_courses.status' => 1, 'rto_courses.activated_now' => 1])
            ->select('is_superseded', 'superseded_date', 'rto_courses.id as Id', 'rto_courses.course_code', DB::raw('CONCAT(rto_courses.course_code,":",rto_courses.course_name) AS Name'));

        $resultArr = $query->groupBy('rto_courses.id')->orderBy('rto_courses.course_code', 'asc')->get()->toArray();

        foreach ($resultArr as $key => $res) {
            $resultArr[$key]['superseded_date_expired'] = ($res['superseded_date']) ? date('d-m-Y', strtotime('+1 year', strtotime($res['superseded_date']))) : '';
            $resultArr[$key]['superseded_date'] = ($res['superseded_date']) ? date('d-m-Y', strtotime($res['superseded_date'])) : '';
        }

        return $resultArr;
    }

    public function getCoursesCampusWise($campusId)
    {
        $query = Courses::join('rto_course_campus', 'rto_course_campus.course_id', '=', 'rto_courses.id')
            ->select('rto_courses.id', 'rto_courses.course_name', 'rto_courses.course_code')
            ->where(['rto_courses.status' => 1, 'rto_courses.activated_now' => 1])
            ->where('rto_courses.campus_list', 'LIKE', '%'.$campusId.'%')
            ->select('rto_courses.is_superseded', 'rto_courses.superseded_date', 'rto_courses.id as Id', DB::raw('CONCAT(rto_courses.course_code,":",rto_courses.course_name) AS Name'));
        $resultArr = $query->groupBy('rto_courses.id')->get()->toArray();

        foreach ($resultArr as $key => $res) {
            $resultArr[$key]['superseded_date_expired_no_format'] = ($res['superseded_date']) ? date('Y-m-d', strtotime('+1 year', strtotime($res['superseded_date']))) : '';
            $resultArr[$key]['superseded_date_expired'] = ($res['superseded_date']) ? date('d-m-Y', strtotime('+1 year', strtotime($res['superseded_date']))) : '';
            $resultArr[$key]['superseded_date'] = ($res['superseded_date']) ? date('d-m-Y', strtotime($res['superseded_date'])) : '';
        }

        return $resultArr;
    }

    public function getCollegeCampusList($collegeId, $courseId)
    {
        if ($courseId == null) {
            return CollegeCampus::select('id as Id', 'name as Name')
                ->where('college_id', '=', $collegeId)
                ->where('status', 1)
                ->get()
                ->toArray();
        } else {
            return CollegeCampus::select('rto_campus.name as Name', 'rto_campus.id as Id')
                ->join('rto_course_campus as rc', 'rc.campus_id', '=', 'rto_campus.id')
                ->where('rto_campus.college_id', $collegeId)
                ->where('rc.course_id', $courseId)
                ->where('rto_campus.status', 1)
                ->get()
                ->toArray();
        }
    }

    public function getResultCalculationMethod($courseId)
    {
        return Courses::select('results_calculation_methods', 'fee_help')
            ->where('college_id', '=', Auth::user()->college_id)
            ->where('id', $courseId)
            ->get()
            ->toArray();
    }

    public function getAgentsByCollegeId($collegeId)
    {
        return Agent::select('id as Id', 'agency_name as Name')
            ->where('college_id', $collegeId)
            ->orderBy('Name', 'asc')
            ->get()
            ->toArray();
    }

    public function getTemplatesByCollegeId($collegeId)
    {
        return CourseTemplate::select('id as Id', 'template_name as Name')
            ->where('college_id', $collegeId)
            ->get()
            ->toArray();
    }

    public function getTemplatesByCollegeAndCourseId($collegeId, $courseId)
    {
        return CourseTemplate::select('id as Id', 'template_name as Name')
            ->where('college_id', '=', $collegeId)
            ->where('course_id', '=', $courseId)
            ->get()
            ->toArray();
    }

    public function getStudyReasons(): array
    {
        return StudyReason::select(['avaitmiss_id as Id', 'title as Name'])->get()->toArray();
    }

    public function checkDuplicateRecord($studentId, $campusId, $courseId, $offerId)
    {
        $query = StudentCourses::where(['student_id' => $studentId, 'course_id' => $courseId])->where('status', '!=', 'Finished')->where('status', '!=', 'Cancelled')->where('status', '!=', 'Deferred');
        if (! empty($offerId) && $offerId != 'new_offer') {
            $query->where('offer_id', $offerId);
        }

        return $query->count();
    }

    public function addStudentOffer($userId, $studentId, $data)
    {
        $studentOfferId = null;

        if ($data['offer_id'] == 'new_offer') {
            if ($this->isValidStudentOfferData($data)) {
                $studentOfferId = $this->createStudentOffer($studentId);
                $this->createStudentServiceInformation($studentId, $studentOfferId, $userId);
            }
        } else {
            $studentOfferId = $data['offer_id'];
        }

        $this->updateStudentDetails($studentId, $studentOfferId);

        return $studentOfferId;
    }

    private function isValidStudentOfferData($data)
    {
        return ! empty($data['agent_id']) && ! empty($data['campus_id']) && ! empty($data['course_id']);
    }

    private function createStudentOffer($studentId)
    {
        return StudentOffers::create([
            'student_id' => $studentId,
            'status' => 'In_process',
        ])->id;
    }

    private function createStudentServiceInformation($studentId, $studentOfferId, $userId)
    {
        StudentServiceInformation::create([
            'student_id' => $studentId,
            'offer_id' => $studentOfferId,
            'created_by' => $userId,
            'updated_by' => $userId,
        ]);
    }

    private function updateStudentDetails($studentId, $studentOfferId)
    {
        StudentDetails::where('student_id', $studentId)->update(['offer_id' => $studentOfferId]);
    }

    public function createStudentCourses($data)
    {
        return StudentCourses::create($data);
    }

    public function findCoursesData($id)
    {
        return Courses::find($id);
    }

    public function find($id)
    {
        return StudentCourses::find($id);
    }

    public function getStudentCoursesByCourse($courseId, $studentIds, $collegeId = null)
    {
        $coursesQuery = Student::with(['studentCourses' => function ($query) use ($courseId) {
            if (is_array($courseId)) {
                $query->whereIn('course_id', $courseId);
            } else {
                $query->where('course_id', $courseId);
            }
        }])->whereIn('id', $studentIds);
        if ($collegeId) {
            $coursesQuery->where('college_id', '=', $collegeId);
        }

        return $coursesQuery->get();
    }

    public function getStudentCoursesById($studentCourseId, $studentIds)
    {
        return Student::with(['studentCourses' => function ($query) use ($studentCourseId) {
            $query->where('id', $studentCourseId);
        }])->whereIn('id', $studentIds)->get();
    }

    public function getStudentEmailContent($studentId)
    {
        $columnArr = [

            DB::raw('concat(rs.name_title, rs.first_name, " ", rs.family_name) as student_name'),
            DB::raw('concat(rst.name_title, rst.first_name, " ", rst.last_name) as account_manager'),
            'rs.first_name',
            'rs.middel_name',
            'rs.family_name',
            'rs.nickname',
            'rs.current_postcode',
            'rs.current_state',
            'rs.current_country',
            'rs.email as student_email',
            'rs.DOB as birth_date',
            'rs.visa_expiry_date',
            'rss.value as visa_type',
            'rs.generated_stud_id',
            'rs.current_mobile_phone',
            'rs.current_postcode',
            'rs.current_state',
            'rs.current_street_name',
            'rs.current_city',
            'rs.name_title',
            'rs.gender',
            'rs.passport_no',
            'rs.visa_status',
            'rs.visa_expiry_date',
            'rcd.fax',
            'country1.name as birth_country',
            'country2.nationality',
            'country3.name as country_name',
            'rs.email as personalEmail',
            'rs.optional_email as AlternateEmail',
            'rsd.emergency_email',
            'clg.contact_email as college_email',
            'clg.college_name as entity_name',
            'clg.RTO_code',
            'clg.CRICOS_code',
            'clg.legal_name',
            'clg.contact_person',
            'clg.contact_phone',
            'clg.college_url',
            'clg.college_logo',
            'clg.college_signature',
            'clg.dean_name',
            'clg.dean_signature',
            'clg.timezone as college_timezone',
            'clg.allow_public_images',
            'rcd.ABN as college_ABN',
            'rs.student_type',
            'rst.first_name as teacher_first_name',
            'rst.last_name as teacher_last_name',
            'rst.email as teacher_email',
            'rst.mobile as teacher_mobile',
            'rs.current_street_no',
            'rs.current_unit_detail',
            'rs.current_building_name',
            'rs.current_building_name',
        ];
        $sql = Student::withoutGlobalScope(\Illuminate\Database\Eloquent\SoftDeletingScope::class)
            ->from('rto_students as rs')
            ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rs.id')
            ->leftjoin('rto_country as country1', 'country1.id', '=', 'rs.birth_country')
            ->leftjoin('rto_country as country2', 'country2.id', '=', 'rs.nationality')
            ->leftjoin('rto_country as country3', 'country3.id', '=', 'rs.current_country')
            ->leftjoin('rto_colleges as clg', 'rs.college_id', '=', 'clg.id')
            ->leftjoin('rto_college_details as rcd', 'rcd.college_id', '=', 'clg.id')
            ->leftjoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rsd.account_manager_id')
            ->leftjoin('rto_setup_section as rss', 'rss.id', '=', 'rs.visa_status')
            ->where('rs.id', '=', $studentId)
            ->whereNull('rs.deleted_at');

        $result = $sql->select($columnArr)->get()->toarray();

        return $result;
    }

    public function getStudentCoursesEmailContent($studentId, $courseId, $studentCourseId = '')
    {
        $columnArr = [
            'rc.results_calculation_methods',
            'rc.cricos_code',
            'rc.course_code',
            'rc.course_name',
            'campus.name as campus_name',
            'rct.template_name as courseTemplate',
            DB::raw('concat(rs.name_title, rs.first_name, " ", rs.family_name) as student_name'),
            DB::raw('concat(rst.name_title, rst.first_name, " ", rst.last_name) as account_manager'),
            'rs.first_name',
            'rs.middel_name',
            'rs.family_name',
            'rs.nickname',
            'rs.current_postcode',
            'rs.current_state',
            'rs.current_country',
            'rs.email as student_email',
            'rs.DOB as birth_date',
            'rs.visa_expiry_date',
            'rss.value as visa_type',
            'rs.generated_stud_id',
            'rs.current_mobile_phone',
            'rs.current_postcode',
            'rs.current_state',
            'rs.current_street_name',
            'rs.current_city',
            'rs.name_title',
            'rs.gender',
            'rs.passport_no',
            'rs.visa_status',
            'rs.visa_expiry_date',
            'rs.email as personalEmail',
            'rs.optional_email as AlternateEmail',
            'rs.visa_expiry_date',
            'rs.visa_expiry_date',
            'rcd.fax',
            'country1.name as birth_country',
            'country2.nationality',
            'country3.name as country_name',
            'rsd.emergency_email',
            'clg.contact_email as college_email',
            'clg.college_name as entity_name',
            'rcct.title as course_type',
            'agent.id as agent_id',
            'agent.agency_name as agent_name',
            'rsc.*',
            'clg.RTO_code',
            'clg.CRICOS_code',
            'clg.legal_name',
            'clg.contact_person',
            'clg.contact_phone',
            'clg.college_url',
            'clg.college_logo',
            'clg.college_signature',
            'clg.dean_name',
            'clg.dean_signature',
            'clg.allow_public_images',
            'clg.timezone as college_timezone',
            'rcd.ABN as college_ABN',
            'rs.student_type',
            'rst.first_name as teacher_first_name',
            'rst.last_name as teacher_last_name',
            'rst.email as teacher_email',
            'rst.mobile as teacher_mobile',
            'agent.agency_name',
            'agent.contact_person as agent_name',
            'agent.primary_email as agent_email',
            'agent.telephone as agent_telephone',
            'rs.current_street_no',
            'rs.current_unit_detail',
            'rs.current_building_name',
            'rs.current_building_name',
            'rsc.start_date',
            'rsc.finish_date',
            'rsc.total_weeks',
        ];
        $sql = StudentCourses::from('rto_student_courses as rsc')
            ->leftjoin('rto_campus as campus', 'campus.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rsc.student_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_course_template as rct', 'rct.id', '=', 'rsc.course_template')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->leftjoin('rto_country as country1', 'country1.id', '=', 'rs.birth_country')
            ->leftjoin('rto_country as country2', 'country2.id', '=', 'rs.nationality')
            ->leftjoin('rto_country as country3', 'country3.id', '=', 'rs.current_country')
            ->leftjoin('rto_colleges as clg', 'rs.college_id', '=', 'clg.id')
            ->leftjoin('rto_college_details as rcd', 'rcd.college_id', '=', 'clg.id')
            ->leftjoin('rto_college_course_type as rcct', 'rcct.id', '=', 'rsc.course_type_id')
            ->leftjoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rsd.account_manager_id')
            ->leftjoin('rto_agents as agent', 'agent.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_setup_section as rss', 'rss.id', '=', 'rs.visa_status')
            ->where('rsc.student_id', '=', $studentId);
        if ($courseId != '') {
            $sql->where('rsc.course_id', '=', $courseId);
        }
        if ($studentCourseId != '') {
            $sql->where('rsc.id', '=', $studentCourseId);
        }
        $result = $sql->select($columnArr)->get()->toarray();

        return $result;
    }

    public function getArrayStudentEnrolledCourseName($studentId, $studentCourseId = '')
    {
        return StudentCourses::join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where([
                // 'rto_student_courses.student_id' => $studentId,
                'rto_student_courses.offer_status' => 'Enrolled',
            ])
            ->when(! empty($studentCourseId), function ($query) use ($studentCourseId) {
                return $query->where('rto_student_courses.id', $studentCourseId);
            }, function ($query) use ($studentId) {
                return $query->where('rto_student_courses.student_id', $studentId);
            })
            ->get([
                'rto_courses.course_name',
                'rto_courses.id',
                'rto_courses.course_code',
                'rto_student_courses.start_date',
                'rto_student_courses.finish_date',
                'rto_student_courses.total_weeks',
                'rto_student_courses.status',
            ])->toArray();
    }

    public function getArrayStudentOfferedCourseName($studentId, $studentCourseId = '')
    {
        return StudentCourses::join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where([
                // 'rto_student_courses.student_id' => $studentId,
                'rto_student_courses.offer_status' => 'Offered',
            ])
            ->when(! empty($studentCourseId), function ($query) use ($studentCourseId) {
                return $query->where('rto_student_courses.id', $studentCourseId);
            }, function ($query) use ($studentId) {
                return $query->where('rto_student_courses.student_id', $studentId);
            })
            ->get([
                'rto_courses.course_name',
                'rto_courses.id',
                'rto_courses.course_code',
                'rto_student_courses.start_date',
                'rto_student_courses.finish_date',
                'rto_student_courses.total_weeks',
                'rto_student_courses.status',
            ])->toArray();
    }

    public function emailTemplateDocModel($emailExistAttachmentIdArr)
    {
        return EmailTemplateDocuments::whereIn('id', $emailExistAttachmentIdArr)->get()->toArray();
    }

    public function letterTemplateDocModel($letterExistAttachmentIdArr)
    {
        return ReportLetterFileAttachment::whereIn('id', $letterExistAttachmentIdArr)->get()->toArray();
    }

    public function saveFailedEmails($collegeId, $arrFailMail)
    {

        $obj = new FailedEmails;
        $obj->college_id = $collegeId;
        $obj->sender = $arrFailMail['sender'];
        $obj->receiver = is_array($arrFailMail['receiver']) ? json_encode($arrFailMail['receiver']) : $arrFailMail['receiver'];
        $obj->subject = $arrFailMail['subject'];
        $obj->content = is_array($arrFailMail['content']) ? json_encode($arrFailMail['content']) : $arrFailMail['content'];
        $obj->error_message = $arrFailMail['error_message'];
        $obj->error_code = $arrFailMail['error_code'];
        if ($obj->save()) {
            return true;
        } else {
            return false;
        }
    }

    public function getStudentsWithCourseById($studentIdArr, $studentCourseId)
    {
        return $this->model->with(['studentCourses' => function ($q) use ($studentCourseId) {
            $q->where('id', $studentCourseId);
        }])->whereIn('id', $studentIdArr)->get();
    }

    public function getStudentsWithCourseByCourseId($studentIdArr, $courseId)
    {
        return $this->model->with(['studentCourses' => function ($q) use ($courseId) {
            $q->where('course_id', '=', $courseId);
        }])->whereIn('id', $studentIdArr)->get();
    }

    public function getStudentDetails($studentId)
    {
        $columnArr = [
            'rs.id',
            'rs.student_type',
            DB::raw("CONCAT_WS(' ', rs.first_name, rs.family_name) as full_name"),
            'rs.generated_stud_id',
            'rs.college_id',
            'rs.gender',
            'rs.first_name',
            'rs.middel_name',
            'rs.family_name',
            'rs.email',
            'rs.optional_email',
            'ru.email as login_email',
            'rs.current_home_phone',
            'rs.current_work_phone',
            'rs.current_mobile_phone',
            'rs.DOB',
            'rs.birthplace',
            'rs.birth_country',
            'rs.nationality',
            'rs.passport_no',
            'rs.passport_expiry',
            'rs.visa_number',
            'rs.visa_expiry_date',
            'rsd.emergency_contact_person',
            'rsd.emergency_relationship',
            'rsd.emergency_address',
            'rsd.emergency_phone',
            'rsd.emergency_email',
            'rsd.scs',
            'rs.USI',
            'rs.is_usi_verified',
            'rs.usi_invalid_reason',
            'rs.is_apply_usi',
            'rs.current_country',
            'rs.current_building_name',
            'rs.current_unit_detail',
            'rs.current_street_no',
            'rs.current_street_name',
            'rs.current_city',
            'rs.current_state',
            'rs.current_postcode',
            'rs.is_postal_address',
            'rs.postal_country',
            'rs.postal_building_name',
            'rs.postal_unit_detail',
            'rs.postal_street_no',
            'rs.postal_street_name',
            'rs.postal_city',
            'rs.permanent_country',
            'rs.permanent_street_name',
            'rs.postal_state',
            'rs.postal_postcode',
            'rs.profile_picture',
            'rs.visa_status',
            'agent.agency_name as agent_name',
            'nat.nationality as birth_nationality_name',
            'c1.name as birth_country_name',
            'c2.name as postal_country_name',
            'c3.name as current_country_name',
            'c4.name as permanent_country_name',
            'c3.name as country_name',

            // DB::raw("CONCAT_WS(', ',rs.permanent_street_name, rs.permanent_city, rs.permanent_state, rs.permanent_postcode,c4.name) as address")
            DB::raw("CONCAT_WS(', ', rs.current_street_no, rs.current_street_name, rs.current_city, rs.current_state, rs.current_postcode, c3.name) as address"),
        ];

        $result = Student::alias('rto_students as rs')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rs.id')
            ->leftjoin('rto_agents as agent', 'agent.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_users as ru', 'ru.username', '=', 'rs.generated_stud_id')
            ->leftjoin('rto_country as nat', 'nat.id', '=', 'rs.nationality')
            ->leftjoin('rto_country as c1', 'c1.id', '=', 'rs.birth_country')
            ->leftjoin('rto_country as c2', 'c2.id', '=', 'rs.postal_country')
            ->leftjoin('rto_country as c3', 'c3.id', '=', 'rs.current_country')
            ->leftjoin('rto_country as c4', 'c4.id', '=', 'rs.permanent_country')
            ->where([
                'rs.id' => $studentId,
                'rsd.student_id' => $studentId,
            ])
            ->select($columnArr)
            ->get()
            ->first();

        $result->DOB = date('d-m-Y', strtotime($result->DOB));
        $arrSurveyContactStatusDiv = Config::get('constants.arrSurveyContactStatusDiv');
        $result->scs_value = $result->scs ? ($arrSurveyContactStatusDiv[$result->scs] ?? '-') : '-';

        return $result;
    }

    public function updateStudent($studentId, $data)
    {
        return Student::where('id', $studentId)->update($data);
    }

    public function updateProfilePicture($studentId, $profilePicture)
    {

        // TODO::GNG-1808 (If use below code for update data, observer not working)
        /* return Student::where('id',$studentId)->update(['profile_picture' => $profilePicture]); */

        $studentObject = Student::find($studentId);
        $studentObject->profile_picture = $profilePicture;

        return $studentObject->save();
    }

    private function getColorCode($status)
    {
        $bgColor = 'gray';
        if ($status == 'Current Student') {
            $bgColor = 'primary-blue';
        } elseif ($status == 'Cancelled') {
            $bgColor = 'red';
        } elseif ($status == 'Transitioned') {
            $bgColor = 'yellow';
        } elseif ($status == 'Completed') {
            $bgColor = 'green';
        } elseif ($status == 'Finished') {
            $bgColor = 'green';
        } elseif ($status == 'Withdrawn') {
            $bgColor = 'pink';
        } elseif ($status == 'Suspended') {
            $bgColor = 'red';
        }

        return $bgColor;
    }

    public function getUserAuditLogData($request, $countOnly = false)
    {
        $post = ($request->input()) ? $request->input() : [];

        // $whereArr = [];

        $columnArr = [
            'global_activity_log.log_name',
            'global_activity_log.description',
            'global_activity_log.subject_type',
            'global_activity_log.subject_id',
            'global_activity_log.event',
            'global_activity_log.causer_type',
            'global_activity_log.causer_id',
            'global_activity_log.properties',
            'global_activity_log.created_at',
            // DB::raw("DATE_FORMAT(global_activity_log.created_at, '%d-%m-%Y %H:%i:%s  %p') as created_at_formatted"),
            'ru.name as create_by_name',
        ];

        $columns = [
            'log_name' => 'log_name',
            'description' => 'description',
            'event' => 'event',
        ];

        $query = UserAuditLog::leftjoin('rto_users as ru', 'ru.id', '=', 'global_activity_log.causer_id')
            // ->where($whereArr)
            ->select($columnArr)
            ->orderBy('global_activity_log.id', 'DESC');

        $this->gridDataFilter($query, $post, $columns);

        $this->gridDataSorting($query, $post);

        $result = $this->gridDataPaginationV2($query, $post, $countOnly);

        if (! $countOnly) {
            foreach ($result['data'] as $k => $row) {
                $result['data'][$k]['event'] = ucfirst($row['event']);
                $result['data'][$k]['properties'] = $this->setAttributeFiled(json_decode($row['properties'], true));
                $result['data'][$k]['SN'] = ($k + 1);
                $result['data'][$k]['created_at_formatted'] = Helpers::convertDateTimeToReadableFormat($row['created_at']);
            }
        }

        return $result;
    }

    public function enrollTemplateUnit($post)
    {
        $getUnitLists = $this->getSubjectUnitForEnrollTrait($post);

        foreach ($getUnitLists as $getUnitList) {
            $unitDetail = UnitModule::find($getUnitList['value']);
            $post['unit_id'] = $getUnitList['value'];
            $post['subject_id'] = $unitDetail->subject_id;
            $post['updated_by'] = Auth::user()->id;
            $post['created_by'] = Auth::user()->id;
            $enrollmentId = StudentSubjectEnrolment::create($post);

            $unitData = [];
            $unitData['unit_id'] = $getUnitList['value'];
            $unitData['student_subject_enrollment_id'] = $enrollmentId->id;
            $unitData['college_id'] = $enrollmentId->college_id;
            StudentUnitEnrollment::create($unitData);
        }

        return true;
    }

    public function getUnitDetailList($studentId, $courseId, $studentCourseId = '')
    {
        $arrStudentSubjectEnrollment = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rsse.student_id')
            ->leftjoin('rto_student_unit_enrollment as rsue', 'rsue.student_subject_enrollment_id', '=', 'rsse.id')
            ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rsse.unit_id')
            ->leftjoin('rto_subject as rs', 'rs.id', '=', 'rsse.subject_id')
            ->leftjoin('rto_venue as rv', 'rv.id', '=', 'rsse.vanue_location')
            ->where(['rsse.student_id' => $studentId, 'rsse.course_id' => $courseId])
            ->withStudentCourseId($studentCourseId)
            ->get(['rsu.id', 'rsu.unit_code', 'rsu.vet_unit_code', 'rsu.unit_name', 'rsue.compentency', 'rs.subject_code', 'rv.postcode', 'rto_students.birth_country', 'rsse.*']);
        $enrolledUnitList = '';
        if (! empty($arrStudentSubjectEnrollment)) {
            $enrolledUnitList = '<ul>';
            foreach ($arrStudentSubjectEnrollment as $value) {
                $enrolledUnitList .= '<li>'.$value['unit_code'].' : '.$value['unit_name'].'</li>';
            }
            $enrolledUnitList .= '</ul>';
        }

        return $enrolledUnitList;
    }
}
