<?php

namespace Tests\Unit;

use App\Services\CertificateContentReplacer;
use PHPUnit\Framework\TestCase;

class CertificateSignatureParametersTest extends TestCase
{

    public function test_certificate_content_replacer_handles_new_signatures()
    {
        // Test data with new signature parameters
        $testData = [
            'college' => [
                'admission_manager_signature' => '<img src="data:image/png;base64,test" alt="Admission Manager Signature" style="height: auto; width: 80px;" />',
                'student_support_signature' => '<img src="data:image/png;base64,test" alt="Student Support Signature" style="height: auto; width: 80px;" />'
            ]
        ];

        $replacer = new CertificateContentReplacer($testData);

        // Test template with new signature placeholders
        $template = '<div>
            <p>Admission Manager: [college.admission_manager_signature]</p>
            <p>Student Support: [college.student_support_signature]</p>
        </div>';

        $result = $replacer->replace($template);

        // Assert that placeholders are replaced with actual signature HTML
        $this->assertStringContainsString('Admission Manager Signature', $result);
        $this->assertStringContainsString('Student Support Signature', $result);
        $this->assertStringNotContainsString('[college.admission_manager_signature]', $result);
        $this->assertStringNotContainsString('[college.student_support_signature]', $result);
    }

    public function test_certificate_content_replacer_handles_empty_signatures()
    {
        // Test data with empty signature parameters
        $testData = [
            'college' => [
                'admission_manager_signature' => '',
                'student_support_signature' => ''
            ]
        ];

        $replacer = new CertificateContentReplacer($testData);

        // Test template with new signature placeholders
        $template = '<div>
            <p>Admission Manager: [college.admission_manager_signature]</p>
            <p>Student Support: [college.student_support_signature]</p>
        </div>';

        $result = $replacer->replace($template);

        // Assert that placeholders are replaced with empty strings
        $this->assertStringContainsString('Admission Manager: </p>', $result);
        $this->assertStringContainsString('Student Support: </p>', $result);
        $this->assertStringNotContainsString('[college.admission_manager_signature]', $result);
        $this->assertStringNotContainsString('[college.student_support_signature]', $result);
    }

    public function test_certificate_content_replacer_handles_missing_signature_data()
    {
        // Test data without signature parameters
        $testData = [
            'college' => [
                'name' => 'Test College'
            ]
        ];

        $replacer = new CertificateContentReplacer($testData);

        // Test template with new signature placeholders
        $template = '<div>
            <p>Admission Manager: [college.admission_manager_signature]</p>
            <p>Student Support: [college.student_support_signature]</p>
        </div>';

        $result = $replacer->replace($template);

        // Assert that placeholders are replaced with empty strings when data is missing
        // This is the expected behavior of data_get() function
        $this->assertStringContainsString('Admission Manager: </p>', $result);
        $this->assertStringContainsString('Student Support: </p>', $result);
        $this->assertStringNotContainsString('[college.admission_manager_signature]', $result);
        $this->assertStringNotContainsString('[college.student_support_signature]', $result);
    }
}
