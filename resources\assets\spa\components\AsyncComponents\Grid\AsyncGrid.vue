<template>
    <async-grid-filters
        class="async-grid-filter"
        :store="store"
        :has-search="hasSearch"
        :has-filters="hasFilters"
        :has-bulk-actions="hasBulkActions"
        :has-export="hasExport"
        :has-reset-filters="hasRestFilters"
        :has-create-action="hasCreateAction"
        :create-btn-label="createBtnLabel"
        @handel-reset="emit('handelReset')"
        @handle-export="emit('handleExport')"
        :filter-columns="filterColumns"
        :has-manage-columns="hasManageColumns"
        :has-all-select="hasAllSelect"
        :columns="columns"
        :has-refresh-action="hasRefreshAction"
        :search-placeholder="searchPlaceholder"
    >
        <template v-slot:header-actions>
            <slot name="header-actions" />
        </template>
        <template v-if="hasFilters" v-slot:filters>
            <slot name="filters" />
        </template>
        <template v-slot:bulk-actions>
            <slot name="bulk-actions" />
            <VDropdown
                ref="settingDropdown"
                :distance="6"
                :triggers="['click']"
                :auto-hide="true"
                :placement="'bottom-end'"
                v-if="hasSettings"
                popper-class="min-w-[200px]"
            >
                <Button variant="secondary" class="relative" :loading="refreshing">
                    <icon name="setting-line" fill="currentColor" width="16" height="16" />
                    <span
                        v-if="store.filters.onlyArchived"
                        class="absolute -right-2 -top-2 inline-flex h-5 min-w-6 shrink-0 items-center justify-center rounded-full bg-primary-blue-50 text-xs font-bold leading-5 text-primary-blue-500"
                    >
                        1
                    </span>
                </Button>
                <template #popper>
                    <div class="p-3">
                        <h3 class="mb-4 text-base font-semibold">View Settings</h3>
                        <slot name="settings" />
                    </div>
                </template>
            </VDropdown>
        </template>
        <template
            v-if="hasManageColumns && columns.some((col) => col.canHide)"
            v-slot:manage-column
        >
            <grid-column-selector
                :columns="columns"
                :visible-count="visibleCount"
                :total-columns="columns.length"
                :is-column-visible="isColumnVisible"
                :toggle-column="toggleColumn"
                :toggle-all-columns="toggleAllColumns"
                :reset-columns="resetColumns"
            />
        </template>
    </async-grid-filters>
    <grid-wrapper
        :fullWidth="false"
        :rowHover="true"
        :rounded="false"
        :striped="false"
        :class="{ 'flex-1 overflow-y-auto': isDesktop }"
        :loading="store.loading"
    >
        <grid
            :style="gridStyle"
            :loading="store.loading"
            :class="'async-grid'"
            :loader="'loaderTemplate'"
            :data-items="store.all"
            :columns="gridColumns"
            :skip="
                props.pageable
                    ? (store.serverPagination.page - 1) * store.serverPagination.rowsPerPage
                    : 0
            "
            :take="props.pageable ? store.serverPagination.rowsPerPage : store.all.length"
            :total="props.pageable ? store.serverPagination.rowsNumber : store.all.length"
            :selected-field="enableSelection ? 'selected' : undefined"
            :selection-mode="enableSelection ? 'multiple' : undefined"
            @selectionchange="onSelectionChange"
            @headerselectionchange="onHeaderSelectionChange"
            @rowclick="onRowClick"
            @pagechange="onPageChange"
            :pageable="gridPageable"
            :scrollable="true"
            :resizable="true"
            :sortable="{ allowUnsort: false }"
            :sort="sort"
            @sortchange="onSortChange"
            :filter="filter"
            @filterchange="onFilterChange"
        >
            <template #loaderTemplate>
                <table-loader v-if="store.loading" />
            </template>
            <GridNoRecords>
                <empty-state />
            </GridNoRecords>
            <template #sortingHeaderCell="{ props }">
                <div class="flex cursor-pointer items-center justify-between gap-2 truncate">
                    {{ props.title }}
                    <div class="flex flex-1 justify-end gap-1">
                        <SortingIndicator :dir="getDirection(props.field)" v-if="props.sortable" />
                        <VDropdown
                            ref="filterDropdown"
                            :distance="6"
                            :triggers="['click']"
                            :auto-hide="true"
                            :placement="'bottom-end'"
                            v-if="currentColumn(props.field)?.filterable"
                            popper-class="min-w-[500px]"
                        >
                            <icon name="grid-filter" fill="currentColor" />
                            <template #popper>
                                <GridColumnFilter
                                    v-model="store.serverPagination.column_filters[props.field]"
                                    :column="currentColumn(props.field)"
                                    :config="getColumnFilterConfig(props.field)"
                                    @applyFilters="
                                        () => {
                                            store.fetchPaged();
                                        }
                                    "
                                >
                                    <template #actions>
                                        <GridSortAction :field="props.field" :store="store" />
                                    </template>
                                </GridColumnFilter>
                            </template>
                        </VDropdown>
                    </div>
                </div>
            </template>
            <template
                v-for="(col, i) in replacedHeaderCells"
                :key="col?.name || i"
                v-slot:[`${col.name}HeaderCell`]="{ props }"
            >
                <div class="flex cursor-pointer items-center justify-between gap-2 truncate">
                    {{ props.title }}
                    <div class="flex flex-1 justify-end gap-1">
                        <VDropdown
                            ref="filterDropdown"
                            :distance="6"
                            :triggers="['click']"
                            :auto-hide="true"
                            :placement="'bottom-end'"
                            v-if="currentColumn(props.field)?.filterable"
                            popper-class="min-w-[200px]"
                        >
                            <div class="flex items-center gap-1">
                                <icon name="grid-filter" fill="currentColor" />
                                <span
                                    v-if="store.filters[props.field]"
                                    class="inline-flex h-5 min-w-6 shrink-0 items-center justify-center rounded-full bg-primary-blue-50 text-xs font-bold leading-5 text-primary-blue-500"
                                >
                                    {{ store.filters[props.field]?.length }}
                                </span>
                            </div>
                            <template #popper="{ shown }">
                                <!-- Always use custom filter for replaced columns -->
                                <slot
                                    :name="`filter-${props.field}`"
                                    :props="{
                                        field: props.field,
                                        title: props.title,
                                        column: currentColumn(props.field),
                                        value: store.filters[props.field],
                                        options: getFilterOptions(props.field),
                                        onFilterChange: (value) =>
                                            handleFilterChange(props.field, value),
                                        onApplyFilters: () => {
                                            store.fetchPaged();
                                        },
                                    }"
                                    :shown="shown"
                                />
                            </template>
                        </VDropdown>
                    </div>
                </div>
            </template>

            <template #selectedTemplate="{ props }">
                <td v-bind="props">
                    <Checkbox
                        :checked="!!store.selected?.find((item) => item.id === props.dataItem.id)"
                        @update:checked="
                            (val) => {
                                if (val) {
                                    store.selected.push(props.dataItem);
                                } else {
                                    store.selected = store.selected.filter(
                                        (item) => item.id !== props.dataItem.id
                                    );
                                }
                            }
                        "
                    ></Checkbox>
                </td>
            </template>
            <template #selectedHeaderTemplate="{ props }">
                <Checkbox
                    v-bind="props"
                    :checked="getSelectAllState"
                    @update:checked="
                        (val) => {
                            console.log('val', val);
                            if (val) {
                                store.selected = [...store.all];
                            } else {
                                store.selected = [];
                            }
                        }
                    "
                    :intermediate-value="'mixed'"
                ></Checkbox>
            </template>
            <template #actionsTemplate="{ props }">
                <GridActionsBlock
                    :row="props.dataItem"
                    :actions="hasMoreActions ? inlineActions : actions"
                    :store="store"
                    :tdAttrs="{
                        class: props.class,
                        colspan: props.colspan,
                        role: props.role,
                        'aria-selected': props['aria-selected'],
                        'data-grid-col-index': props['data-grid-col-index'],
                        id: props.id,
                    }"
                    @download="(row) => emit('download', row)"
                    v-bind="props"
                >
                    <template #actions="{ row }">
                        <!-- Apply new behavior only if actionDisplayMode is provided -->
                        <template v-if="actionDisplayMode">
                            <!-- Any custom slot actions -->
                            <slot name="actions" :row="row" v-if="!hasMoreActions" />

                            <!-- More Actions dropdown -->
                            <VDropdown
                                v-if="dropdownActions.length"
                                :distance="4"
                                :triggers="['click']"
                                :auto-hide="true"
                                :placement="'left-start'"
                                :popup-class="'tw-action-dropdown'"
                                @apply-show="onMenuShow(row.id)"
                                @apply-hide="onMenuHide"
                            >
                                <GridActionButton
                                    :tooltip-title="'More Actions'"
                                    :class="{
                                        'bg-primary-blue-50 ring-1 ring-primary-blue-500 ring-offset-2':
                                            activeRow === row.id,
                                    }"
                                    :disabled="store.selected.length > 0"
                                >
                                    <icon name="more-horizontal" />
                                </GridActionButton>

                                <template #popper>
                                    <div class="flex flex-col">
                                        <GridActionsDropdown
                                            :key="props.dataItem.id"
                                            :row="props.dataItem"
                                            :actions="dropdownActions"
                                            :selected-rows="store.selected"
                                            :store="store"
                                        />
                                        <slot name="actions" :row="row" />
                                    </div>
                                </template>
                            </VDropdown>
                        </template>

                        <!-- 🧩 Original untouched behavior -->
                        <template v-else>
                            <slot name="actions" :row="row" v-if="!hasMoreActions" />
                            <VDropdown
                                :distance="4"
                                :triggers="['click']"
                                :auto-hide="true"
                                :placement="'left-start'"
                                v-if="hasMoreActions"
                                :popup-class="'tw-action-dropdown'"
                                @apply-show="onMenuShow(row.id)"
                                @apply-hide="onMenuHide"
                            >
                                <GridActionButton
                                    :tooltip-title="'More Actions'"
                                    :class="{
                                        'bg-primary-blue-50 ring-1 ring-primary-blue-500 ring-offset-2':
                                            activeRow === row.id,
                                    }"
                                    :disabled="store.selected.length > 0"
                                >
                                    <icon name="more-horizontal" />
                                </GridActionButton>

                                <template #popper>
                                    <div class="flex flex-col">
                                        <GridActionsDropdown
                                            :key="props.dataItem.id"
                                            :row="props.dataItem"
                                            :actions="actions"
                                            :selected-rows="store.selected"
                                            :store="store"
                                        />
                                        <slot name="actions" :row="row" />
                                    </div>
                                </template>
                            </VDropdown>
                        </template>
                    </template>
                </GridActionsBlock>
            </template>

            <template v-slot:defaultCellTemplate="{ props }">
                <slot
                    :name="`default-cell`"
                    :props="{
                        ...props,
                        rowForm: getRowForm(props.dataIndex),
                    }"
                >
                    <td>
                        <template v-if="typeof formatCellData(props.columnIndex) === 'function'">
                            {{ formatCellData(props.columnIndex)(props.dataItem[props.field]) }}
                        </template>
                        <template v-else>
                            <template
                                v-if="
                                    props.dataItem[props.field] === '' ||
                                    props.dataItem[props.field] === null
                                "
                            >
                                -
                            </template>
                            <template v-else>
                                {{ props.dataItem[props.field] ?? '-' }}
                            </template>
                        </template>
                    </td>
                </slot>
            </template>
            <template
                v-for="(col, i) in replacedColumns"
                :key="col?.name || i"
                v-slot:[`${col?.name}Template`]="{ props }"
            >
                <td v-bind="props">
                    <slot
                        :name="`body-cell-${col?.name}`"
                        :props="{
                            ...props,
                            rowForm: getRowForm(props.dataIndex),
                        }"
                    ></slot>
                </td>
            </template>
        </grid>
    </grid-wrapper>
    <grid-floating-actions
        v-if="hasFloatingActions"
        :store="store"
        :has-bulk-delete="hasBulkDelete"
        :has-bulk-export="hasBulkExport"
    >
        <template v-if="hasFloatingActions" v-slot:floating-actions>
            <slot name="floating-actions" />
        </template>
    </grid-floating-actions>
</template>

<script setup>
import GridWrapper from '@spa/components/KendoGrid/GridWrapper';
import TableLoader from '@spa/components/KendoGrid/TableLoader';
import EmptyState from '@spa/components/KendoGrid/EmptyState';
import { ref, computed, watch } from 'vue';
import {
    Grid,
    GridColumnMenuCheckboxFilter,
    GridColumnMenuFilter,
    GridColumnMenuSort,
    GridNoRecords,
} from '@progress/kendo-vue-grid';
import AsyncGridFilters from '@spa/components/AsyncComponents/Grid/Partials/AsyncGridFilters.vue';
import GridActionsBlock from '@spa/components/AsyncComponents/Grid/Partials/GridActionsBlock.vue';
import GridFloatingActions from '@spa/components/AsyncComponents/Grid/Partials/GridFloatingActions.vue';
import { useColumnVisibility } from '@spa/services/validators/useColumnVisibility.js';
import GridColumnSelector from '@spa/components/AsyncComponents/Grid/Partials/GridColumnSelector.vue';
import GridActionsDropdown from '@spa/components/AsyncComponents/Grid/Partials/GridActionDropdown.vue';
import GridActionButton from '@spa/components/AsyncComponents/Grid/Partials/GridActionButton.vue';
import GridColumnFilter from '@spa/components/AsyncComponents/Grid/Partials/GridColumnFilter.vue';
import GridSortAction from '@spa/components/AsyncComponents/Grid/Partials/GridSortAction.vue';
import Checkbox from '@spa/components/Checkbox.vue';
import { PagerNumericButtons } from '@progress/kendo-vue-data-tools';
import SortingIndicator from '@spa/components/KendoGrid/SortingIndicator.vue';
import { useMediaQuery } from '@spa/services/useMediaQuery.js';
import Button from '@spa/components/Buttons/Button.vue';

const props = defineProps({
    store: { type: Object, required: true },
    columns: { type: Array, default: () => [] },
    enableSelection: {
        type: Boolean,
        default: false,
    },
    gridStyle: {
        default: { height: '70vh' },
    },
    selectedField: {
        type: String,
        default: 'selected',
    },
    hasSearch: { type: Boolean, default: true },
    hasFilters: { type: Boolean, default: true },
    hasBulkActions: { type: Boolean, default: false },
    hasExport: { type: Boolean, default: true },
    hasRestFilters: { type: Boolean, default: true },
    hasCreateAction: { type: Boolean, default: true },
    hasSection: { type: Boolean, default: true },
    hasActions: { type: Boolean, default: true },
    hasFloatingActions: { type: Boolean, default: false },
    hasBulkDelete: { type: Boolean, default: true },
    hasBulkExport: { type: Boolean, default: true },
    hasManageColumns: { type: Boolean, default: true },
    hasAllSelect: { type: Boolean, default: false },
    hasMoreActions: { type: Boolean, default: false },
    hasRefreshAction: { type: Boolean, default: false },
    hasHeaderAction: { type: Boolean, default: true },
    hasSettings: { type: Boolean, default: false },
    actions: {
        type: Array,
        default: () => [],
    },
    actionsConfig: {
        type: Object,
        default: () => ({
            width: 150,
        }),
    },
    actionDisplayMode: {
        type: Object,
        default: () => null,
    },
    hasStickyAction: { type: Boolean, default: false },
    pageable: { type: Boolean, default: true },
    filterColumns: { type: Number, default: 3 },
    storageKey: { type: String, default: 'grid-columns' },
    createBtnLabel: { type: String, default: 'Add New' },
    searchPlaceholder: { type: String, default: 'Enter Keyword' },
});

const emit = defineEmits(['handelReset', 'download', 'rowclick', 'handleExport']);

const isDesktop = useMediaQuery('(min-width: 1024px)');

// State
const sort = ref([]);
const filter = ref({
    filters: [],
    logic: 'and',
});
const headerCheckboxState = ref(false);
const activeRow = ref(null);

// Computed
const replacedColumns = computed(() => {
    return Array.isArray(props.columns) ? props.columns.filter((col) => col.replace) : [];
});

const replacedHeaderCells = computed(() => {
    return Array.isArray(props.columns) ? props.columns.filter((col) => col.customFilter) : [];
});

const gridPageable = computed(() => {
    if (!props.store.showPagination) return false;
    return {
        buttonCount: 5,
        info: true,
        type: 'numeric',
        pageSizes: true,
        previousNext: true,
    };
});

const gridColumns = computed(() => {
    const newColumns = [];
    if (props.enableSelection) {
        newColumns.push({
            field: 'selected',
            name: 'selected',
            cell: 'selectedTemplate',
            headerCell: 'selectedHeaderTemplate',
            width: '50px',
            filterable: false,
            sortable: false,
            selectable: true,
            locked: true,
        });
    }
    if (Array.isArray(visibleColumns.value)) {
        visibleColumns.value.forEach((column) => {
            const newColumn = { ...column };
            if (column.replace) {
                newColumn.cell = `${column.name}Template`;
            } else {
                newColumn.cell = 'defaultCellTemplate';
            }
            if (column.filterable) {
                newColumn.sortable = false;
            }
            if (column.sortable || column.filterable) {
                if (column.customFilter) {
                    newColumn.headerCell = `${column.name}HeaderCell`;
                } else {
                    newColumn.headerCell = 'sortingHeaderCell';
                }
            } else {
                newColumn.sortable = false;
            }
            if (!column.name) {
                newColumn.name = column.field;
            }
            newColumns.push(newColumn);
        });
    }
    if (props.hasActions) {
        newColumns.push({
            name: 'actions',
            title: 'Actions',
            field: 'actions',
            cell: 'actionsTemplate',
            actions: props.actions,
            locked: props.hasStickyAction,
            sortable: false,
            minResizableWidth: 200,
            ...props.actionsConfig,
        });
    }
    if (
        Array.isArray(visibleColumns.value) &&
        visibleColumns.value.every((item) => 'width' in item)
    ) {
        newColumns.push({
            sortable: false,
        });
    }
    return newColumns;
});

// Methods
const getDirection = (field) => {
    const sortBy = props.store.serverPagination.sortBy;
    const sortField = props.columns.find((item) => item.field === field);
    if (!sortField) return null;
    const sortDirection = props.store.serverPagination.descending ? 'desc' : 'asc';
    if (sortBy === field) {
        return sortDirection;
    }
    return null;
};
const getRowForm = (dataIndex) => {
    const dataIndexStart =
        props.store.serverPagination.rowsPerPage * (props.store.serverPagination.page - 1);
    const currentIndex = dataIndex - dataIndexStart - 1;
    return props.store.form_rows[currentIndex] ?? {};
};

const formatCellData = (columnIndex) => {
    const column = props.columns[columnIndex];
    return column?.formatCellData;
};

const currentColumn = (field) => {
    return gridColumns.value.find((col) => col.field === field);
};

const onPageChange = (event) => {
    if (event.event.type === 'scroll') {
        return;
    }

    if (!props.store.selected.length) {
        props.store.onSelectAll(false);
    }

    // header selection
    const page = Math.floor(event.page.skip / event.page.take) + 1;
    props.store.serverPagination.page = page;
    props.store.serverPagination.rowsPerPage = event.page.take;
    props.store.fetchPaged();
};

const onSelectionChange = (event) => {
    const checked = event?.event?.target?.checked;
    if (!checked) {
        props.store.allSelected = false;
    }
    if (!props.store.selected) {
        props.store.selected = [];
    }
    if (!props.store.excluded) {
        props.store.excluded = [];
    }
    if (checked) {
        const existsInSelected = props.store.selected.find(
            (item) => item?.id === event.dataItem?.id
        );
        if (!existsInSelected) {
            props.store.selected.push(event.dataItem);
        }
        props.store.excluded = props.store.excluded.filter(
            (item) => item?.id !== event.dataItem?.id
        );
    } else {
        props.store.selected = props.store.selected.filter(
            (item) => item?.id !== event.dataItem?.id
        );

        const existsInExcluded = props.store.excluded.find(
            (item) => item?.id === event.dataItem?.id
        );
        if (!existsInExcluded) {
            props.store.excluded.push(event.dataItem);
        }
    }
};

const onRowClick = (event) => {
    emit('rowclick', event);
};

const onHeaderSelectionChange = (event) => {
    const checked = event.event.target.checked;
    if (!checked) {
        store.onSelectAll(false);
    }
    props.store.all = props.store.all.map((item) => ({
        ...item,
        selected: checked,
    }));
    props.store.selected = checked ? props.store.all : [];
};

const onSortChange = (event) => {
    sort.value = event.sort;
    const field = event.sort[0]?.field;
    const isDescending = event.sort[0]?.dir === 'desc';
    fetchSorted(field, isDescending);
};

const onColumnSortChange = (newDescriptor, event) => {
    console.log('desc', newDescriptor);
    sort.value = newDescriptor;
    const field = newDescriptor[0]?.field;
    const isDescending = newDescriptor[0]?.dir === 'desc';
    fetchSorted(field, isDescending);
};

const onFilterChange = (newDescriptor, event) => {
    console.log('filter change', newDescriptor);
    filter.value = newDescriptor;

    // Update the store filters
    if (newDescriptor.filters && newDescriptor.filters.length > 0) {
        // Convert filter descriptor to store filters format
        const storeFilters = {};
        newDescriptor.filters.forEach((f) => {
            if (f.field && f.value !== undefined) {
                storeFilters[f.field] = f.value;
            }
        });
        props.store.filters = { ...props.store.filters, ...storeFilters };
    } else {
        // Clear filters if no filters are applied
        props.store.filters = {};
    }

    // Trigger data refresh
    props.store.fetchPaged();
};

const fetchSorted = (field, isDescending) => {
    props.store.serverPagination.sortBy = field;
    props.store.serverPagination.descending = isDescending;
    props.store.fetchPaged();
};

const onMenuShow = (id) => {
    activeRow.value = id;
};

const onMenuHide = () => {
    activeRow.value = null;
};

const getSelectAllState = computed(() => {
    if (props.store.allSelected) {
        return true;
    }

    if (props.store.selected?.length === 0) {
        return false;
    }
    if (props.store.selected?.length === props.store.all?.length) {
        return true;
    }
    return 'mixed';
});
const columnVisibilityRef = ref(null);

const {
    visibleColumns,
    visibleCount,
    toggleColumn,
    toggleAllColumns,
    resetColumns,
    isColumnVisible,
    initializeVisibility,
} = useColumnVisibility(props.columns, props.storageKey);

// Function to get column filter config
const getColumnFilterConfig = (field) => {
    const column = currentColumn(field);
    if (!column || !column.filterable) {
        return [];
    }

    // If column has filterableConfig, use it to build the available fields
    if (column.filterableConfig) {
        // Return an array with the current column as the available field
        return [
            {
                text: column.title,
                value: field,
                ...column.filterableConfig,
            },
        ];
    }

    // Default configuration
    return [
        {
            text: column.title,
            value: field,
            type: 'string',
        },
    ];
};

const handleFilterChange = (field, value) => {
    if (!props.store.filters[field]) {
        props.store.filters[field] = {};
    }
    props.store.filters[field] = value;
};

const getFilterOptions = (field) => {
    const column = currentColumn(field);
    return column?.filterableConfig?.options || [];
};

const inlineActions = computed(() => {
    if (!props.actionDisplayMode?.inline?.length) return [];
    return props.actions.filter((a) => props.actionDisplayMode.inline.includes(a));
});

const dropdownActions = computed(() => {
    if (!props.actionDisplayMode?.dropdown?.length) return [];
    return props.actions.filter((a) => props.actionDisplayMode.dropdown.includes(a));
});

// Watchers
watch(
    () => props.columns,
    () => {
        initializeVisibility();
    },
    { deep: true }
);

watch(
    () => props.store.filters,
    () => {
        props.store.fetchPaged();
    },
    { deep: true }
);

watch(
    () => props.store.serverPagination,
    () => {
        //props.store.fetchPaged();
    },
    { deep: true }
);
</script>
