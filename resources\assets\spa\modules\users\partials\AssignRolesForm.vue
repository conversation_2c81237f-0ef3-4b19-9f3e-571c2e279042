<template>
    <AsyncForm
        :type="'kendo'"
        :layout="{ cols: 1, gap: 16 }"
        :orientation="'vertical'"
        dialog-position="center"
        :dialogTitle="dialogTitle"
        :store="store"
        :has-custom-submit="true"
        @submit="onSubmit"
        :max-width="'576px'"
        :submit-text="'Assign Role'"
    >
        <div class="p-6 pt-4">
            <AsyncUserGroupSelect
                name="roles"
                label="Roles"
                v-model="formData.roles"
                :validation-message="store.errors?.roles"
                :valid="!store.errors?.roles"
                :touched="true"
                :indicaterequired="true"
                :multiple="true"
                :filters="{ type: activeTab }"
                :style="{
                    maxWidth: '100%',
                }"
            />
        </div>
    </AsyncForm>
</template>
<script setup>
import { watch, computed } from 'vue';
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncFormPopup.vue';
import { useUsersStore } from '@spa/stores/modules/users/useUsersStore.js';
import { storeToRefs } from 'pinia';
import AsyncUserGroupSelect from '@spa/modules/user-group/UserGroupSelect.vue';

const props = defineProps({
    userTypeStore: Object,
    row: Object,
});

const store = useUsersStore();
const { formData } = storeToRefs(store);

const activeTab = computed(() => {
    const typeMappings = {
        staff: 'team-members',
        teacher: 'trainers',
        agent: 'agents',
        employer: 'employers',
        serviceprovider: 'service-providers',
        placementprovider: 'placement-providers',
        'agent-staff': 'agent-staffs',
        student: 'students',
    };

    return typeMappings[props.userTypeStore.userType] || 'team-members';
});

const onSubmit = async () => {
    formData.value.roles = normalizeRoles(formData.value.roles);
    formData.value.id = props.row.id;
    formData.value.user_type = props.userTypeStore.userType;
    await store.onAssignRoles(formData.value);
    props.userTypeStore.fetchPaged();
};

const normalizeRoles = (roles) => {
    if (Array.isArray(roles)) {
        return roles.map((role) => (typeof role === 'object' ? role.id : role));
    }
    return roles;
};

const dialogTitle = computed(() => {
    let label = props.row?.name;
    if (store.selected.length > 1) {
        label = `${store.selected.length} users`;
    }
    return `Assign Roles to ${label}`;
});

watch(
    () => store.formDialog,
    (val) => {
        if (val) {
            formData.value = {
                ...formData.value,
                roles: props.row?.roles.map((role) => role.id),
            };
        }
    }
);
</script>
