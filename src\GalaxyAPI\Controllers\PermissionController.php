<?php

namespace GalaxyAPI\Controllers;

use App\Users;
use GalaxyAPI\Requests\PermissionRequest;
use GalaxyAPI\Resources\PermissionResource;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Support\Auth\Permission;

class PermissionController extends CrudBaseController
{
    public function init()
    {
        $request = request();
        if ($request->filled('filters')) {
            $filters = json_decode($request->query('filters'), true);
        }
        $userId = $filters['user'] ?? null;
        $roleId = $filters['role'] ?? null;
        try {
            $userId = decryptIt($userId);
            $roleId = decryptIt($roleId);
        } catch (\Exception $e) {
            $userId = $roleId = 0;
        }

        $commonLoads = [
            'group_permissions',
            'permissionOf',
            // 'children',
            // 'parent',
        ];
        $this->withAll = [
            ...$commonLoads,
        ];
        $this->loadAll = [
            ...$commonLoads,
        ];
        // get the role and user type

        $this->scopeWithValue = [
            'userRole' => ['user' => $userId, 'role' => $roleId],
        ];
    }

    public function __construct()
    {
        parent::__construct(
            model: Permission::class,
            storeRequest: PermissionRequest::class,
            updateRequest: PermissionRequest::class,
            resource: PermissionResource::class,
        );
    }

    public function index()
    {
        $request = request();
        if ($request->filled('filters')) {
            $filters = json_decode($request->query('filters'), true);
        }
        $userId = $filters['user'] ?? null;
        $roleId = $filters['role'] ?? null;
        $newrole = (int) @$filters['newrole'] ?? null;
        try {
            $userId = decryptIt($userId);
            if ($newrole) {
                $roleId = (int) $roleId;
            } else {
                $roleId = decryptIt($roleId);
            }
        } catch (\Exception $e) {
            $userId = $roleId = 0;
        }
        $rowsPerPage = $request->input('rowsPerPage', 10);
        if ($rowsPerPage > $this->maxRowsPerPage) {
            $rowsPerPage = $this->maxRowsPerPage;
        }
        $permissions = $this->model::getPermissionsListForUserRole($userId, $roleId, $rowsPerPage, $newrole);

        // dd($permissions);
        return $this->resource::collection($permissions)
            ->additional([
                'code' => Response::HTTP_OK,
                'success' => 1,
            ]);
    }

    public function getModules(Request $request)
    {
        return ajaxSuccess([
            'data' => Permission::pluck('module', 'module')->unique()->values()->toArray(),
            '',
        ]);
    }

    public function updateAccess(PermissionRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->DTO();
            $user = Users::find($data->user_id);
            if (! $user) {
                throw new \Exception('User not found');
            }
            $newRole = $user->updatePermissions($data);
            $userRoles = $user->roles()->with('parentRole')->get()?->map(function ($role) use ($data) {
                $parentRole = $role?->parentRole ?? null;

                return [
                    'id' => $role?->id,
                    'secure_id' => encryptIt($role?->id),
                    'parent_role_id' => $parentRole?->id ?? null,
                    'parent_role_secure_id' => $parentRole?->id ? encryptIt($parentRole?->id) : null,
                    'name' => $parentRole?->name ?? $role?->name,
                    'is_current' => $role->id == $data->role_id,
                ];
            });

            return ajaxSuccess(['data' => ['roles' => $userRoles], 'message' => 'Permissions updated successfully.']);
        } catch (\Exception $e) {
            DB::rollback();

            return ajaxError($e->getMessage());
        } finally {
            DB::commit();
        }
    }
}
