<?php

namespace App\Constants\Menu;

use Support\Auth\Access;

class AgentSpaMenu
{
    public const AGENT_MENU_ITEMS = [
        [
            'label' => 'Student',
            'url' => '#',
            'gap_after' => true,
            'mainmenu' => ['students', '', 'commission', 'document', 'finance', 'reports', 'communication', 'partner_agent'],
            'activeurls' => [
                'spa.agent.studentList',
                'spa.agent.requestedStudent',
                'spa.agent.offeredStudent',
                'spa.agent.staffApplications',
                'spa.agent.continueApplication',
                'spa.agent.newApplication',
                'spa.agent.gteDashboard',
                'spa.agent.offerCommunication',
                'spa.agent.communicationArchives',
            ],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M11.9343 2.02637C11.8132 1.99121 11.6882 1.99121 11.5671 2.02637L3.44172 4.52649C3.17999 4.60853 3.0003 4.85073 3.0003 5.12418C3.0003 5.39763 3.17999 5.63983 3.44172 5.72186L4.25036 5.97187V8.77279L3.03155 12.4253C2.96904 12.6167 3.0003 12.8277 3.11749 12.9917C3.23468 13.1558 3.42219 13.2496 3.62533 13.2496H6.12545C6.32468 13.2496 6.51609 13.1519 6.63329 12.9917C6.75048 12.8316 6.78173 12.6206 6.71923 12.4292L5.50042 8.77279V6.3547L11.5671 8.22198C11.6882 8.25714 11.8132 8.25714 11.9343 8.22198L20.0597 5.72186C20.3215 5.63983 20.5012 5.39763 20.5012 5.12418C20.5012 4.85073 20.3215 4.60853 20.0597 4.52649L11.9343 2.02637ZM11.7507 6.97192L5.75043 5.12418L11.7507 3.27643L17.751 5.12418L11.7507 6.97192ZM10.3483 19.1796C11.0944 20.0194 12.407 20.0194 13.1531 19.1796L15.9306 16.0544C17.8526 16.7576 19.2276 18.5936 19.2511 20.7499H4.25036C4.26989 18.5936 5.64496 16.7536 7.57083 16.0544L10.3483 19.1796ZM7.28175 14.8278C4.79335 15.6716 3.0003 18.0271 3.0003 20.8007C3.0003 21.4648 3.53938 22 4.19957 22H19.3019C19.966 22 20.5012 21.4609 20.5012 20.8007C20.5012 18.0271 18.7081 15.6716 16.2197 14.8317C15.7939 14.6871 15.3329 14.8473 15.036 15.1833L12.2195 18.3475C11.9695 18.6287 11.532 18.6287 11.2859 18.3475L8.46931 15.1794C8.17242 14.8434 7.71146 14.6832 7.28566 14.8278H7.28175ZM4.87539 10.851L5.25822 11.9995H4.49256L4.87539 10.851ZM6.75048 8.04619V8.87436C6.75048 11.6362 8.98887 13.8746 11.7507 13.8746C14.5126 13.8746 16.751 11.6362 16.751 8.87436V8.04619L15.5009 8.42903V8.87436C15.5009 10.9448 13.8211 12.6245 11.7507 12.6245C9.68031 12.6245 8.00054 10.9448 8.00054 8.87436V8.43293L6.75048 8.0501V8.04619Z" fill="currentColor"/>
            </svg>',
            'sub_menu' => [
                [
                    'label' => 'Student List',
                    'url' => '/spa/agent/student-list',
                    'subactiveurls' => ['spa.agent.offerCommunication'],
                    'permissions' => [
                        Access::AP_STUDENT_LIST_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Student Applications',
                    'url' => '/spa/agent/requested-student',
                    'subactiveurls' => [
                        'spa.agent.offeredStudent',
                        'spa.agent.continueApplication',
                        'spa.agent.gteDashboard',
                        'spa.agent.offerCommunication',
                    ],
                    'permissions' => [
                        Access::AP_STUDENT_APPLICATIONS_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Offer Communication log',
                    'url' => '/spa/agent/communication-archives',
                    'subactiveurls' => ['spa.agent.offerCommunication'],
                    'permissions' => [
                        Access::AP_COMMUNICATION_ARCHIVES_ACCESS->value,
                    ],
                ],
                // [
                //     'label' => 'Create New',
                //     'url' => '/spa/agent/new-application',
                // ],
                // [
                //     'label' => 'Staff Application',
                //     'url' => '/spa/agent/staff-application',
                // ],
                // [
                //     'label' => 'Continue a saved Online Application',
                //     'url' => '/spa/agent/continue-application',
                // ],
            ],
        ],
        [
            'label' => 'Commission',
            'url' => '/spa/agent/commission',
            'mainmenu' => ['students', '', 'commission', 'document', 'finance', 'reports', 'communication', 'partner_agent'],
            'activeurls' => ['spa.agent.commission', 'upload-view-document-checklist', 'add-docs-ecoe-request'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M6.09701 4.9748L20.7641 0.97643L23.2666 10.3139L22.0504 10.2151L20.3094 3.73611L20.2905 3.65672C20.211 3.37597 20.0237 3.14024 19.7699 3.0013C19.5161 2.86236 19.2164 2.83158 18.9368 2.91572L18.3981 3.07628L18.3935 3.05839L15.8466 3.84446L6.44146 6.2512L6.09701 4.9748ZM5.56415 11.685L5.2973 12.0178C4.58757 12.9061 3.43497 14.3501 3.01503 14.853C2.98905 14.8827 2.95957 14.9092 2.92727 14.9317C2.89921 14.9549 2.86706 14.9725 2.83257 14.9835C2.63848 15.0632 2.45022 15.157 2.26932 15.2642C2.13965 15.3383 2.02176 15.4318 1.9198 15.5416C1.86953 15.596 1.83199 15.661 1.81002 15.7318C1.79282 15.7984 1.79384 15.8683 1.81299 15.9338C2.18699 17.3921 2.61156 18.8922 2.94842 20.3544C3.00893 20.5561 3.11329 20.741 3.25397 20.8959C3.39464 21.0508 3.56812 21.1717 3.76189 21.25C4.12297 21.3997 4.49656 21.5179 4.87857 21.6031C5.27539 21.7272 5.68028 21.8246 6.09061 21.8947C6.50354 21.9573 6.92548 21.9432 7.33564 21.8529L8.56577 21.483C8.58488 21.4738 8.60488 21.4667 8.62543 21.4617C8.66103 21.4518 8.76486 21.423 8.9155 21.3909C9.70989 21.1802 10.6834 20.9182 10.6303 20.203L10.5247 19.7947L10.239 19.858L9.87458 19.9239C9.6768 19.9644 9.49165 20.0045 9.31772 20.0511C9.19971 20.0839 9.07434 20.0682 8.9692 20.0075C8.86407 19.9468 8.78777 19.846 8.7571 19.7275C8.72643 19.6089 8.7439 19.4822 8.80567 19.3752C8.86744 19.2682 8.96844 19.1897 9.08646 19.157C9.27335 19.1052 9.49003 19.0579 9.71341 19.0119L9.74088 19.0075C10.4941 18.8465 11.3512 18.6696 11.3162 18.0915L11.1921 17.6117C11.0783 17.6448 10.9629 17.672 10.8475 17.6992C10.7321 17.7264 10.5596 17.7663 10.4142 17.7939L10.3789 17.7988C10.1859 17.8396 10.0049 17.877 9.82984 17.9255C9.7714 17.9417 9.71055 17.9462 9.65075 17.9387C9.59095 17.9312 9.53338 17.9119 9.48132 17.8819C9.42926 17.8518 9.38373 17.8116 9.34734 17.7636C9.31095 17.7155 9.2844 17.6606 9.26922 17.6019C9.25403 17.5432 9.25051 17.4818 9.25884 17.4214C9.26717 17.3609 9.2872 17.3026 9.31779 17.2496C9.34837 17.1966 9.38891 17.1501 9.43708 17.1127C9.48526 17.0752 9.54013 17.0476 9.59857 17.0314C9.77953 16.9812 9.99324 16.9347 10.2199 16.8831L10.2544 16.8751C11.0065 16.7161 11.8648 16.5372 11.8316 15.9602C11.7873 15.7952 11.7412 15.6291 11.7029 15.4625L10.3738 15.831C10.2558 15.8637 10.1305 15.848 10.0253 15.7873C9.92018 15.7266 9.84388 15.6259 9.81322 15.5073C9.78255 15.3888 9.80002 15.2621 9.86179 15.1551C9.92355 15.0481 10.0246 14.9696 10.1426 14.9369L12.8125 14.1965C13.0582 14.1354 13.2874 14.0178 13.4819 13.8528C13.643 13.7179 13.7563 13.5338 13.8042 13.329C13.8173 13.2664 13.8232 13.2026 13.8218 13.1389C13.8205 13.0775 13.8122 13.0166 13.7971 12.9573C13.7819 12.8969 13.7594 12.8388 13.7301 12.7842C13.7002 12.7296 13.6646 12.6784 13.6238 12.6316C13.4829 12.4805 13.2958 12.3814 13.0915 12.3496C12.8345 12.3133 12.5719 12.3348 12.3225 12.4127L5.89479 14.211C5.77677 14.2437 5.6514 14.228 5.54627 14.1673C5.44113 14.1066 5.36484 14.0059 5.33417 13.8873C5.3035 13.7688 5.32097 13.6421 5.38274 13.5351C5.4445 13.4281 5.5455 13.3496 5.66352 13.3169L5.94128 13.2415L5.55558 11.6826L5.56415 11.685ZM7.43018 12.8334L11.2452 11.7755L11.208 11.7363C10.95 11.445 10.7838 11.0822 10.73 10.6932C10.6762 10.3042 10.7373 9.90611 10.9056 9.54845C11.0739 9.1908 11.342 8.88939 11.6766 8.68173C12.0111 8.47406 12.3974 8.36931 12.7872 8.38051C13.1771 8.39171 13.5533 8.51837 13.8692 8.74472C14.185 8.97106 14.4265 9.28711 14.5635 9.65354C14.7006 10.02 14.7272 10.4206 14.64 10.8055C14.5528 11.1905 14.3558 11.5428 14.0733 11.8186C14.1552 11.8833 14.2312 11.9552 14.3005 12.0335C14.3875 12.1319 14.463 12.2401 14.5254 12.356C14.5881 12.4742 14.637 12.5994 14.6712 12.7293C14.6859 12.786 14.6976 12.8435 14.7063 12.9016L18.3497 11.8562C18.2697 11.522 18.324 11.1675 18.5007 10.8695C18.6774 10.5715 18.9624 10.354 19.2938 10.264L18.4599 6.89219C18.127 6.97879 17.7757 6.92994 17.4823 6.75624C17.1888 6.58254 16.9769 6.29802 16.8925 5.96447L7.72213 8.63351C7.80598 8.96662 7.75588 9.32162 7.58266 9.62165C7.40944 9.92167 7.12705 10.1426 6.79664 10.2365L7.43952 12.8388L7.43018 12.8334ZM14.1241 14.5228C13.8129 14.8095 13.4374 15.0151 13.031 15.1214L12.596 15.2404L12.7302 15.759L12.7403 15.8105L12.7456 15.8617C12.7904 16.1379 12.7483 16.4227 12.6251 16.6762C12.502 16.9296 12.3041 17.1392 12.0589 17.2755L12.0683 17.2809C12.0813 17.3084 12.0924 17.3368 12.1014 17.3659L12.2314 17.8745C12.2369 17.8911 12.2404 17.9084 12.2415 17.926L12.2471 17.9723C12.2949 18.3035 12.2244 18.6428 12.0481 18.9299C11.8943 19.162 11.6816 19.3485 11.4326 19.4695L11.564 19.9713L11.573 20.0247L11.5768 20.07C11.729 21.5929 10.3112 21.9685 9.16709 22.2714C9.08447 22.3023 8.9624 22.3281 8.87043 22.3536C8.46039 22.4769 7.94313 22.6651 7.54318 22.7536C6.4209 23.0057 5.59527 22.7699 4.64028 22.4949C4.19967 22.3972 3.77001 22.2569 3.35718 22.0761C3.04956 21.9417 2.77601 21.7404 2.55589 21.4862C2.33576 21.2321 2.17446 20.9314 2.08339 20.6054L0.94401 16.2003C0.881183 15.9877 0.875258 15.7613 0.926831 15.5439C0.986092 15.3232 1.09694 15.1193 1.25002 14.9496C1.3929 14.7897 1.55707 14.6507 1.7377 14.5364C1.9498 14.3998 2.17299 14.2818 2.40476 14.1838C2.71401 13.8121 3.47622 12.8502 4.18156 11.9694C4.60049 11.438 4.9978 10.9397 5.28782 10.5798L4.66697 8.06884L19.3042 3.93661L21.4123 12.4683L14.1241 14.5228Z" fill="currentColor"/>
            </svg>',
            'permissions' => [
                Access::AP_COMMISSION_ACCESS->value,
            ],
        ],
        [
            'label' => 'Document',
            'url' => '/spa/agent/document',
            'mainmenu' => ['students', '', 'commission', 'document', 'finance', 'reports', 'communication', 'partner_agent'],
            'activeurls' => ['spa.agent.document'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18.5 20C18.5 20.275 18.276 20.5 18 20.5H6C5.724 20.5 5.5 20.275 5.5 20V4C5.5 3.725 5.724 3.5 6 3.5H12V8C12 9.104 12.896 10 14 10H18.5V20ZM13.5 4.621L17.378 8.5H14C13.724 8.5 13.5 8.275 13.5 8V4.621ZM19.414 8.414L13.585 2.586C13.559 2.56 13.527 2.54 13.5 2.516C13.429 2.452 13.359 2.389 13.281 2.336C13.241 2.309 13.195 2.291 13.153 2.268C13.082 2.228 13.012 2.184 12.937 2.152C12.74 2.07 12.528 2.029 12.313 2.014C12.266 2.011 12.22 2 12.172 2H12.171H12H6C4.896 2 4 2.896 4 4V20C4 21.104 4.896 22 6 22H18C19.104 22 20 21.104 20 20V10V9.828C20 9.298 19.789 8.789 19.414 8.414Z" fill="currentColor"/>
            </svg>',
            'permissions' => [
                Access::AP_DOCUMENT_ACCESS->value,
            ],
        ],
        [
            'label' => 'Payments',
            'url' => '/spa/agent/approved-comission',
            'mainmenu' => ['students', '', 'commission', 'document', 'finance', 'reports', 'communication', 'partner_agent'],
            'activeurls' => ['spa.agent.approvedcommission'],
            'svgicon' => '<svg width="24" height="18" viewBox="0 0 24 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_75_9507)">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M23.7441 7.97266C23.9102 8.25 24 8.54688 24 8.85352C24 10.4824 21.4805 11.8027 18.375 11.8027C15.2695 11.8027 12.75 10.4824 12.75 8.85352C12.75 8.54688 12.8398 8.25 13.0059 7.97266C13.7207 9.16992 15.8535 10.041 18.375 10.041C20.8965 10.041 23.0293 9.17188 23.7441 7.97266ZM10.9941 12.3652C11.1602 12.6445 11.25 12.9395 11.25 13.2461C11.25 14.875 8.73047 16.1953 5.625 16.1953C2.51953 16.1953 0 14.875 0 13.2461C0 12.9395 0.0898438 12.6426 0.255859 12.3652C0.970703 13.5625 3.10547 14.4336 5.625 14.4336C8.14648 14.4336 10.2793 13.5645 10.9941 12.3652ZM6.44141 8.05273L6.42969 7.32227C6.5957 7.31641 6.73047 7.26562 6.83398 7.17188C6.93945 7.07812 6.99609 6.96484 7.00781 6.83203C7.02148 6.67969 6.9707 6.54688 6.85742 6.42969C6.74414 6.31445 6.56055 6.24414 6.3125 6.22266C6.08008 6.20312 5.90039 6.24023 5.77539 6.33398C5.65039 6.42773 5.58008 6.55859 5.56641 6.72656C5.54883 6.93555 5.625 7.13086 5.79492 7.3125L5.6582 7.89453L3.7168 7.34961L3.88281 5.42773L4.5625 5.48633L4.44336 6.85742L5.07812 7.02734C5.01172 6.85742 4.98438 6.68945 5 6.51953C5.02734 6.19727 5.16992 5.93359 5.42188 5.73047C5.67578 5.52734 5.99023 5.44141 6.36523 5.47461C6.67773 5.50195 6.94727 5.61523 7.17578 5.81836C7.48828 6.09375 7.62695 6.45117 7.58789 6.88867C7.55664 7.23828 7.43945 7.51562 7.23242 7.7207C7.02734 7.92773 6.76367 8.03906 6.44141 8.05273ZM5.625 5.12305C8.17773 5.12305 10.248 5.93555 10.248 6.93555C10.248 7.9375 8.17773 8.74805 5.625 8.74805C3.07227 8.74805 1.00195 7.93945 1.00195 6.9375C1.00195 5.93555 3.07227 5.12305 5.625 5.12305ZM5.625 4.52344C8.73047 4.52344 11.25 5.84375 11.25 7.47266C11.25 9.10156 8.73047 10.4219 5.625 10.4219C2.51953 10.4199 0 9.09961 0 7.47266C0 5.84375 2.51953 4.52344 5.625 4.52344ZM11.0137 9.4375C11.166 9.70508 11.25 9.99023 11.25 10.2832C11.25 11.9121 8.73047 13.2324 5.625 13.2324C2.51953 13.2324 0 11.9121 0 10.2852C0 9.99023 0.0820312 9.70703 0.236328 9.43945C0.931641 10.6543 3.08008 11.541 5.625 11.541C8.16992 11.5391 10.3203 10.6543 11.0137 9.4375ZM20.4062 1.87891L20.3418 2.61133L17.5801 2.37109C17.8086 2.66016 17.9648 2.99219 18.0527 3.36523L17.3887 3.30664C17.3438 3.11133 17.2422 2.89258 17.0879 2.6543C16.9316 2.41406 16.7422 2.24414 16.5156 2.14062L16.5684 1.54688L20.4062 1.87891ZM18.375 0.732422C20.9277 0.732422 22.998 1.54492 22.998 2.54492C22.998 3.54688 20.9277 4.35742 18.375 4.35742C15.8223 4.35742 13.752 3.54492 13.752 2.54492C13.752 1.54297 15.8223 0.732422 18.375 0.732422ZM18.375 0.130859C21.4805 0.130859 24 1.45117 24 3.08008C24 4.70898 21.4805 6.0293 18.375 6.0293C15.2695 6.0293 12.75 4.70898 12.75 3.08008C12.75 1.45117 15.2695 0.130859 18.375 0.130859ZM23.7637 5.04492C23.916 5.3125 24 5.59766 24 5.89062C24 7.51953 21.4805 8.83984 18.375 8.83984C15.2695 8.83984 12.75 7.51953 12.75 5.89062C12.75 5.5957 12.832 5.3125 12.9863 5.04492C13.6797 6.25977 15.8301 7.14648 18.375 7.14648C20.9199 7.14844 23.0703 6.26172 23.7637 5.04492ZM23.709 13.9824C23.8965 14.2773 24 14.5918 24 14.9199C24 16.5488 21.4805 17.8691 18.375 17.8691C15.2695 17.8691 12.75 16.5488 12.75 14.9199C12.75 14.5918 12.8516 14.2773 13.041 13.9824C13.7891 15.1504 15.8945 15.9941 18.375 15.9941C20.8555 15.9941 22.9629 15.1523 23.709 13.9824ZM23.7363 10.9492C23.9082 11.2324 24 11.5312 24 11.8438C24 13.4727 21.4805 14.793 18.375 14.793C15.2695 14.793 12.75 13.4727 12.75 11.8438C12.75 11.5312 12.8437 11.2305 13.0137 10.9492C13.7363 12.1406 15.8633 13.002 18.375 13.002C20.8867 13.002 23.0117 12.1406 23.7363 10.9492Z" fill="currentColor"/>
            </g>
            <defs>
            <clipPath id="clip0_75_9507">
            <rect width="24" height="17.7383" fill="white" transform="translate(0 0.130859)"/>
            </clipPath>
            </defs>
            </svg>',
            'permissions' => [
                Access::AP_PAYMENT_ACCESS->value,
            ],
        ],
        [
            'label' => 'Performance Analytics',
            'url' => '/spa/agent/report',
            'mainmenu' => ['students', '', 'commission', 'document', 'finance', 'reports', 'communication', 'partner_agent'],
            'activeurls' => ['spa.agent.report'],
            'svgicon' => '<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2 1.25C2 0.835786 1.66421 0.5 1.25 0.5C0.835786 0.5 0.5 0.835786 0.5 1.25V15.75C0.5 16.7165 1.2835 17.5 2.25 17.5H16.75C17.1642 17.5 17.5 17.1642 17.5 16.75C17.5 16.3358 17.1642 16 16.75 16H2.25C2.11193 16 2 15.8881 2 15.75V1.25ZM11 3.75C11 3.33579 11.3358 3 11.75 3H16.7515C17.1657 3 17.5015 3.33578 17.5015 3.74999L17.5015 8.75498C17.5015 9.16919 17.1658 9.50498 16.7515 9.50499C16.3373 9.50499 16.0015 9.16921 16.0015 8.755L16.0015 5.55917L10.7803 10.7803C10.4874 11.0732 10.0126 11.0732 9.71967 10.7803L7.75 8.81066L4.53033 12.0303C4.23744 12.3232 3.76256 12.3232 3.46967 12.0303C3.17678 11.7374 3.17678 11.2626 3.46967 10.9697L7.21967 7.21967C7.51256 6.92678 7.98744 6.92678 8.28033 7.21967L10.25 9.18934L14.9393 4.5H11.75C11.3358 4.5 11 4.16421 11 3.75Z" fill="currentColor"/>
</svg>
',
            'permissions' => [
                Access::AP_PERFORMANCE_ANALYTICS_ACCESS->value,
            ],
        ],
        [
            'label' => 'Mailbox',
            'url' => '/spa/agent/sentmail/inbox',
            'mainmenu' => ['students', '', 'commission', 'document', 'finance', 'reports', 'communication', 'partner_agent', 'inbox'],
            'activeurls' => ['spa.agent.inbox'],
            'svgicon' => '
           <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.9999 15.4992C10.9999 14.9364 11.1859 14.417 11.4997 13.9992H5.25229C4.01027 13.9992 3.00342 15.0061 3.00342 16.2481V16.8258C3.00342 17.7185 3.32194 18.582 3.9017 19.2609C5.468 21.0952 7.85401 22.0004 10.9999 22.0004L11.0501 22.0003C11.0172 21.8384 10.9999 21.6709 10.9999 21.4993L10.9999 20.5004C8.26157 20.5004 6.29569 19.7546 5.04239 18.2869C4.69453 17.8795 4.50342 17.3614 4.50342 16.8258V16.2481C4.50342 15.8345 4.8387 15.4992 5.25229 15.4992H10.9999ZM10.9999 2.00391C13.7613 2.00391 15.9999 4.24248 15.9999 7.00391C15.9999 9.76533 13.7613 12.0039 10.9999 12.0039C8.23845 12.0039 5.99988 9.76533 5.99988 7.00391C5.99988 4.24248 8.23845 2.00391 10.9999 2.00391ZM10.9999 3.50391C9.06688 3.50391 7.49988 5.07091 7.49988 7.00391C7.49988 8.9369 9.06688 10.5039 10.9999 10.5039C12.9329 10.5039 14.4999 8.9369 14.4999 7.00391C14.4999 5.07091 12.9329 3.50391 10.9999 3.50391ZM17.5098 18.9261L12.0189 15.7231C12.1534 14.7492 12.9891 13.9993 14 13.9993H21C22.0948 13.9993 22.9841 14.8789 22.9998 15.97L17.5098 18.9261ZM17.737 19.9395L23 17.1056V20.9993C23 22.1038 22.1046 22.9993 21 22.9993H14C12.8954 22.9993 12 22.1038 12 20.9993V16.8698L17.2481 19.9312C17.3985 20.0189 17.5837 20.0221 17.737 19.9395Z" fill="currentColor"/>
            </svg>',
            'permissions' => [
                Access::AP_MAILBOX_ACCESS->value,
            ],
        ],
        [
            'label' => 'Staffs',
            'url' => '/spa/manage-users/agent-staffs',
            'mainmenu' => ['partner_agent'],
            'activeurls' => ['spa.manage-users.agent-staffs'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
	<path fill="currentColor" d="M12.5 10.25a.75.75 0 0 1 .75-.75h3.5a.75.75 0 0 1 0 1.5h-3.5a.75.75 0 0 1-.75-.75m.75 4.75a.75.75 0 1 0 0 1.5h3.5a.75.75 0 1 0 0-1.5zm-2.47-5.22a.75.75 0 1 0-1.06-1.06l-1.47 1.47l-.47-.47a.75.75 0 0 0-1.06 1.06l1 1a.75.75 0 0 0 1.06 0zm0 4.44a.75.75 0 0 1 0 1.06l-2 2a.75.75 0 0 1-1.06 0l-1-1a.75.75 0 1 1 1.06-1.06l.47.47l1.47-1.47a.75.75 0 0 1 1.06 0m5.214-10.136A2.25 2.25 0 0 0 13.75 2h-3.5a2.25 2.25 0 0 0-2.236 2H6.25A2.25 2.25 0 0 0 4 6.25v13.5A2.25 2.25 0 0 0 6.25 22h11.5A2.25 2.25 0 0 0 20 19.75V6.25A2.25 2.25 0 0 0 17.75 4h-1.764zm0 .012L16 4.25q0-.078-.005-.154M10.25 6.5h3.5c.78 0 1.467-.397 1.871-1h2.129a.75.75 0 0 1 .75.75v13.5a.75.75 0 0 1-.75.75H6.25a.75.75 0 0 1-.75-.75V6.25a.75.75 0 0 1 .75-.75h2.129c.404.603 1.091 1 1.871 1m0-3h3.5a.75.75 0 0 1 0 1.5h-3.5a.75.75 0 0 1 0-1.5" />
</svg>',
            'permissions' => [

            ],
        ],
    ];
}
