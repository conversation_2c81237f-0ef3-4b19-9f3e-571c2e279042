<?php

namespace App\Constants\Menu;

use Support\Auth\Access;

class TeacherMenu
{
    public const TEACHER_MENU_ITEMS = [
        [
            'label' => 'Dashboard',
            'url' => '/teacher/dashboard',
            'mainmenu' => ['profile', 'dashboard', 'document', 'material', 'timesheet', 'compliance', 'traineeship', 'communication', 'reports', 'elearning', 'administration', 'clients'],
            'activeurls' => ['teacher_dashboard'],
            'svgicon' => '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M1 10L3 8M3 8L10 1L17 8M3 8V18C3 18.5523 3.44772 19 4 19H7M17 8L19 10M17 8V18C17 18.5523 16.5523 19 16 19H13M7 19C7.55228 19 8 18.5523 8 18V14C8 13.4477 8.44772 13 9 13H11C11.5523 13 12 13.4477 12 14V18C12 18.5523 12.4477 19 13 19M7 19H13" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </svg>',
            'permissions' => [
                Access::TP_DASHBOARD_ACCESS->value,
            ],
        ],
        [
            'label' => 'Tasks Managment',
            'url' => '/teacher/view-task',
            'mainmenu' => ['profile', 'dashboard', 'document', 'material', 'timesheet', 'compliance', 'traineeship', 'communication', 'reports', 'elearning', 'administration', 'clients'],
            'activeurls' => ['view-task'],
            'svgicon' => '<svg width="20" height="20" viewBox="0 0 16 12" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M1 7L5 11L15 1" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </svg>',
            'permissions' => [
                Access::TP_TASKS_MANAGEMENT_ACCESS->value,
            ],
        ],
        [
            'label' => 'Documents',
            'url' => '#',
            'mainmenu' => ['profile', 'dashboard', 'document', 'material', 'timesheet', 'compliance', 'traineeship', 'communication', 'reports', 'elearning', 'administration', 'clients'],
            'activeurls' => ['view-college-document-teacher', 'subject-specific', 'course-specific'],
            'gap_after' => true,
            'svgicon' => '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M3 19H13C14.1046 19 15 18.1046 15 17V7.41421C15 7.149 14.8946 6.89464 14.7071 6.70711L9.29289 1.29289C9.10536 1.10536 8.851 1 8.58579 1H3C1.89543 1 1 1.89543 1 3V17C1 18.1046 1.89543 19 3 19Z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </svg>',
            'sub_menu' => [

                [
                    'label' => 'College Documents',
                    'url' => '/teacher/view-college-document-teacher/0',
                    'permissions' => [
                        Access::TP_COLLEGE_DOCUMENTS_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Subject Specific',
                    'url' => '/teacher/subject-specific/0',
                    'permissions' => [
                        Access::TP_SUBJECT_SPECIFIC_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Course Specific',
                    'url' => '/teacher/course-specific/0',
                    'permissions' => [
                        Access::TP_COURSE_SPECIFIC_ACCESS->value,
                    ],
                ],
            ],
        ],
        [
            'label' => 'View Timetable',
            'url' => '/teacher/timetable',
            'mainmenu' => ['profile', 'dashboard', 'document', 'material', 'timesheet', 'compliance', 'traineeship', 'communication', 'reports', 'elearning', 'administration', 'clients'],
            'activeurls' => ['timetable'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path class="newicon stroke-none" d="M21 6.25C21 4.45507 19.5449 3 17.75 3H6.25C4.45507 3 3 4.45507 3 6.25V17.75C3 19.5449 4.45507 21 6.25 21H12.0218C11.7253 20.5368 11.4858 20.0335 11.3135 19.5H6.25C5.2835 19.5 4.5 18.7165 4.5 17.75V8.5H19.5V11.3135C20.0335 11.4858 20.5368 11.7253 21 12.0218V6.25ZM6.25 4.5H17.75C18.7165 4.5 19.5 5.2835 19.5 6.25V7H4.5V6.25C4.5 5.2835 5.2835 4.5 6.25 4.5ZM23 17.5C23 14.4624 20.5376 12 17.5 12C14.4624 12 12 14.4624 12 17.5C12 20.5376 14.4624 23 17.5 23C20.5376 23 23 20.5376 23 17.5ZM17.5 17.5H19.5C19.7761 17.5 20 17.7239 20 18C20 18.2762 19.7761 18.5 19.5 18.5H17C16.7268 18.5 16.5048 18.2809 16.5001 18.0089L16.5 17.9999V14.9999C16.5 14.7238 16.7239 14.4999 17 14.4999C17.2761 14.4999 17.5 14.7238 17.5 14.9999L17.5 17.5Z" />
                            </svg>',
            'permissions' => [
                Access::TP_VIEW_TIMETABLE_ACCESS->value,
            ],
        ],
        [
            'label' => 'Timesheet',
            'url' => '#',
            'mainmenu' => ['profile', 'dashboard', 'document', 'material', 'timesheet', 'compliance', 'traineeship', 'communication', 'reports', 'elearning', 'administration', 'clients'],
            'activeurls' => ['teacher-timesheet-submission', 'extra-timesheet-submission', 'view-timesheet', 'upload-invoice'],
            'svgicon' => '<svg width="24" height="24"  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>',
            'sub_menu' => [
                [
                    'label' => 'Timesheet Submission',
                    'url' => '/teacher/teacher-timesheet-submission',
                    'permissions' => [
                        Access::TP_TIMESHEET_SUBMISSION_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Extra Timesheet Submission',
                    'url' => '/teacher/extra-timesheet-submission',
                    'permissions' => [
                        Access::TP_EXTRA_TIMESHEET_SUBMISSION_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'View Timesheet',
                    'url' => '/teacher/view-timesheet',
                    'permissions' => [
                        Access::TP_VIEW_TIMESHEET_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Upload Invoice',
                    'url' => '/teacher/upload-invoice/0/0',
                    'permissions' => [
                        Access::TP_UPLOAD_INVOICE_ACCESS->value,
                    ],
                ],
            ],
        ],

        [
            'label' => 'Attendance',
            'url' => '#',
            'mainmenu' => ['profile', 'dashboard', 'document', 'material', 'timesheet', 'compliance', 'traineeship', 'communication', 'reports', 'elearning', 'administration', 'clients'],
            'activeurls' => ['daily-attendance', 'group-attendance'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path class="stroke-none newicon"  d="M11.3135 15.5002C11.4859 14.9667 11.7253 14.4634 12.0219 14.0002H4.25278C3.01076 14.0002 2.00391 15.007 2.00391 16.2491V16.8267C2.00391 17.7195 2.32242 18.583 2.90219 19.2619C4.46849 21.0962 6.8545 22.0013 10.0004 22.0013C10.9314 22.0013 11.7961 21.922 12.5927 21.7629C12.2335 21.3496 11.9256 20.8906 11.6789 20.3957C11.1555 20.466 10.5962 20.5013 10.0004 20.5013C7.26206 20.5013 5.29618 19.7555 4.04287 18.2878C3.69502 17.8805 3.50391 17.3624 3.50391 16.8267V16.2491C3.50391 15.8355 3.83919 15.5002 4.25278 15.5002H11.3135ZM10.0004 2.00488C12.7618 2.00488 15.0004 4.24346 15.0004 7.00488C15.0004 9.76631 12.7618 12.0049 10.0004 12.0049C7.23894 12.0049 5.00036 9.76631 5.00036 7.00488C5.00036 4.24346 7.23894 2.00488 10.0004 2.00488ZM10.0004 3.50488C8.06737 3.50488 6.50036 5.07189 6.50036 7.00488C6.50036 8.93788 8.06737 10.5049 10.0004 10.5049C11.9334 10.5049 13.5004 8.93788 13.5004 7.00488C13.5004 5.07189 11.9334 3.50488 10.0004 3.50488ZM17.5 12.0002C20.5376 12.0002 23 14.4627 23 17.5002C23 20.5378 20.5376 23.0002 17.5 23.0002C14.4624 23.0002 12 20.5378 12 17.5002C12 14.4627 14.4624 12.0002 17.5 12.0002ZM19.5 17.5003H17.5L17.5 15.0002C17.5 14.724 17.2761 14.5002 17 14.5002C16.7239 14.5002 16.5 14.724 16.5 15.0002L16.5 17.9988L16.5 18.0003C16.5 18.2764 16.7239 18.5003 17 18.5003H19.5C19.7761 18.5003 20 18.2764 20 18.0003C20 17.7242 19.7761 17.5003 19.5 17.5003Z" fill="#9CA3AF"/>
                            </svg>',
            'sub_menu' => [
                [
                    'label' => 'Daily Attendance',
                    'url' => '/teacher/daily-attendance',
                    'permissions' => [
                        Access::TP_DAILY_ATTENDANCE_ACCESS->value,
                    ],
                ],
            ],
        ],
        [
            'label' => 'Competency',
            'url' => '#',
            'mainmenu' => ['profile', 'dashboard', 'document', 'material', 'timesheet', 'compliance', 'traineeship', 'communication', 'reports', 'elearning', 'administration', 'clients'],
            'activeurls' => ['setup-assessment-task', 'task-entry', 'task-results-entry', 'result-management', 'teacher-transfer-results', 'teacher-vocational-placement', 'teacher-transfer-results-by-unit'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path class="stroke-none newicon" d="M17.9297 9.91797C18 10.0586 18.0352 10.2109 18.0352 10.375C18.0352 10.6797 17.918 10.9492 17.6836 11.1836C17.4727 11.3945 17.2148 11.5 16.9102 11.5H15.7852V13.75C15.7852 14.3594 15.5625 14.8867 15.1172 15.332C14.6719 15.7773 14.1445 16 13.5352 16H12.4102V17.6875C12.4102 17.8516 12.3516 17.9805 12.2344 18.0742C12.1406 18.1914 12.0117 18.25 11.8477 18.25H11.2852C11.1211 18.25 10.9805 18.1914 10.8633 18.0742C10.7695 17.9805 10.7227 17.8516 10.7227 17.6875V14.3125H13.5352C13.6992 14.3125 13.8281 14.2656 13.9219 14.1719C14.0391 14.0547 14.0977 13.9141 14.0977 13.75V9.8125H16.0664C15.9258 9.4375 15.7031 8.80469 15.3984 7.91406C14.7188 5.89844 14.25 4.70313 13.9922 4.32812C13.5234 3.67187 12.832 3.10937 11.918 2.64062C11.0273 2.17188 10.1836 1.9375 9.38672 1.9375H7.03125C6.02344 1.9375 5.08594 2.18359 4.21875 2.67578C3.375 3.16797 2.73047 3.84766 2.28516 4.71484C1.74609 5.79297 1.58203 6.88281 1.79297 7.98438C2.02734 9.08594 2.56641 10.0117 3.41016 10.7617L3.97266 11.2539V17.6875C3.97266 17.8516 3.91406 17.9805 3.79688 18.0742C3.70312 18.1914 3.57422 18.25 3.41016 18.25H2.84766C2.68359 18.25 2.54297 18.1914 2.42578 18.0742C2.33203 17.9805 2.28516 17.8516 2.28516 17.6875V12.0273C1.67578 11.4648 1.14844 10.7031 0.703125 9.74219C0.257812 8.75781 0.0351562 7.84375 0.0351562 7C0.0351562 6.90625 0.0351562 6.82422 0.0351562 6.75391C0.105469 4.92578 0.832031 3.39062 2.21484 2.14844C3.59766 0.882812 5.21484 0.25 7.06641 0.25H9.38672C10.0664 0.25 10.793 0.390625 11.5664 0.671875C12.3633 0.953125 13.0898 1.32813 13.7461 1.79688C14.4258 2.26562 14.9648 2.78125 15.3633 3.34375C15.6211 3.71875 15.9023 4.32812 16.207 5.17188C16.5352 5.99219 16.8516 6.88281 17.1562 7.84375C17.4844 8.80469 17.7422 9.49609 17.9297 9.91797ZM11.0742 7H9.5625C9.65625 7.1875 9.70312 7.375 9.70312 7.5625C9.70312 7.9375 9.57422 8.25391 9.31641 8.51172C9.05859 8.76953 8.74219 8.89844 8.36719 8.89844C8.20312 8.89844 8.05078 8.875 7.91016 8.82812V10.375H6.11719V8.82812C5.97656 8.875 5.82422 8.89844 5.66016 8.89844C5.28516 8.89844 4.96875 8.76953 4.71094 8.51172C4.45312 8.25391 4.32422 7.9375 4.32422 7.5625C4.32422 7.5625 4.32422 7.55078 4.32422 7.52734C4.32422 7.50391 4.32422 7.49219 4.32422 7.49219C3.71484 7.28125 3.41016 6.85938 3.41016 6.22656C3.41016 5.85156 3.53906 5.53516 3.79688 5.27734C4.07812 4.99609 4.39453 4.85547 4.74609 4.85547C4.74609 4.48047 4.875 4.16406 5.13281 3.90625C5.41406 3.64844 5.74219 3.51953 6.11719 3.51953C6.32812 3.51953 6.55078 3.58984 6.78516 3.73047C7.04297 3.28516 7.41797 3.0625 7.91016 3.0625C8.40234 3.0625 8.77734 3.28516 9.03516 3.73047C9.26953 3.58984 9.49219 3.51953 9.70312 3.51953C9.96094 3.51953 10.207 3.61328 10.4414 3.80078C10.6992 3.96484 10.875 4.16406 10.9688 4.39844H11.0742C11.4492 4.39844 11.7656 4.52734 12.0234 4.78516C12.2812 5.04297 12.4102 5.34766 12.4102 5.69922C12.4102 5.93359 12.3516 6.15625 12.2344 6.36719C12.1172 6.55469 11.9531 6.70703 11.7422 6.82422C11.5312 6.94141 11.3086 7 11.0742 7Z" fill="#9CA3AF"/> </svg>',
            'permissions' => [
                Access::TP_COMPETENCY_ACCESS->value,
            ],
            'sub_menu' => [
                [
                    'label' => 'Result Management',
                    'url' => '/teacher/result-management',
                    'permissions' => [
                        Access::TP_RESULT_MANAGEMENT_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Transfer results',
                    'url' => '/teacher/transfer-results',
                    'permissions' => [
                        Access::TP_TRANSFER_RESULTS_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Vocational Placement Result',
                    'url' => '/teacher/vocational-placement',
                    'permissions' => [
                        Access::TP_VOCATIONAL_PLACEMENT_RESULT_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Transfer results by unit',
                    'url' => '/teacher/transfer-results-by-unit',
                    'permissions' => [
                        Access::TP_TRANSFER_RESULTS_BY_UNIT_ACCESS->value,
                    ],
                ],
            ],
        ],
        [
            'label' => 'Traineeship',
            'url' => '#',
            'gap_after' => true,
            'mainmenu' => ['profile', 'dashboard', 'document', 'material', 'timesheet', 'compliance', 'traineeship', 'communication', 'reports', 'elearning', 'administration', 'clients'],
            'activeurls' => ['traineeship-activity', 'activity-between-date', 'activity-between-dates'],
            'svgicon' => '<svg width="20" height="20"  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>',
            'permissions' => [
                Access::TP_TRAINEESHIP_ACCESS->value,
            ],
            'sub_menu' => [
                [
                    'label' => 'Traineeship Activity',
                    'url' => '/teacher/traineeship-activity',
                    'permissions' => [
                        Access::TP_TRAINEESHIP_ACTIVITY_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Activity Between Dates',
                    'url' => '/teacher/activity-between-dates',
                    'permissions' => [
                        Access::TP_ACTIVITY_BETWEEN_DATES_ACCESS->value,
                    ],
                ],
            ],
        ],
        [
            'label' => 'Communication',
            'url' => '#',
            'mainmenu' => ['profile', 'dashboard', 'document', 'material', 'timesheet', 'compliance', 'traineeship', 'communication', 'reports', 'elearning', 'administration', 'clients'],
            'activeurls' => ['mailing-list', 'view-communication-log', 'email-request-to-college'],
            'svgicon' => '<svg width="20" height="20"  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>',
            'permissions' => [
                Access::TP_COMMUNICATION_ACCESS->value,
            ],
            'sub_menu' => [
                [
                    'label' => 'Mailing List',
                    'url' => '/teacher/mailing-list',
                    'permissions' => [
                        Access::TP_MAILING_LIST_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Communication Log',
                    'url' => '/teacher/view-communication-log',
                    'permissions' => [
                        Access::TP_COMMUNICATION_LOG_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Request to Admin',
                    'url' => '/teacher/email-request-to-college',
                    'permissions' => [
                        Access::TP_REQUEST_TO_ADMIN_ACCESS->value,
                    ],
                ],
            ],
        ],
        [
            'label' => 'Others',
            'url' => '#',
            'mainmenu' => ['profile', 'dashboard', 'document', 'material', 'timesheet', 'compliance', 'traineeship', 'communication', 'reports', 'elearning', 'administration', 'clients'],
            'activeurls' => ['generate-report', 'teacher-evaluation', 'teacher_profile', 'view-teacher-elearning-link-list', 'view-teacher-register-improvement', 'edit-teacher-register-improvement', 'add-teacher-register-improvement', 'leave-info'],
            'svgicon' => '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M4 17L8 1M10 17L14 1M3 6H17M1 12H15" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </svg>',
            'sub_menu' => [
                [
                    'label' => 'Reports',
                    'url' => '/teacher/generate-report',
                    'permissions' => [
                        Access::TP_REPORTS_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Elearning Link',
                    'url' => '/teacher/view-teacher-elearning-link-list',
                    'permissions' => [
                        Access::TP_ELEARNING_LINK_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Continuous Improvement',
                    'url' => '/teacher/view-teacher-register-improvement',
                    'subactiveurls' => ['add-teacher-register-improvement', 'edit-teacher-register-improvement'],
                    'permissions' => [
                        Access::TP_CONTINUOUS_IMPROVEMENT_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Leave Info',
                    'url' => '/teacher/leave-info',
                    'permissions' => [
                        Access::TP_LEAVE_INFO_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Evaluation',
                    'url' => '/teacher/teacher-evaluation',
                    'permissions' => [
                        Access::TP_EVALUATION_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Edit Profile',
                    'url' => '/teacher/profile',
                    'permissions' => [
                        Access::TP_EDIT_PROFILE_ACCESS->value,
                    ],
                ],
            ],
        ],
    ];
}
