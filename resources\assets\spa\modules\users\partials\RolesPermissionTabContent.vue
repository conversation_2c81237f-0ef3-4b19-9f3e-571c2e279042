<template>
    <Card :variant="'compact'" :pt="{ root: 'bg-gray-100' }">
        <template #header>
            <div class="flex items-center gap-2">
                <span class="text-gray-700">
                    <icon name="user-cog" :fill="'currentColor'" :width="'20'" :height="'20'" />
                </span>
                <h2 class="text-lg font-medium">Roles & Permissions</h2>
            </div>
        </template>
        <template #content>
            <div class="flex items-center justify-between">
                <LabelValuePair :label="'Assigned Roles:'" :orientation="'vertical'">
                    <template v-for="(role, index) in userAssignedRoles">
                        <Chip
                            :label="role.name"
                            :removable="otherRolesAssignable"
                            :pt="{
                                root: 'pe-2 gap-4 cursor-pointer' + getClassForActive(role, index),
                                icon: 'text-primary-blue-400 hover:text-primary-blue-600 cursor-pointer',
                            }"
                            @click="setCurrentRole(role)"
                            @remove="removeRole(role)"
                        />
                    </template>
                    <UserGroupDropdownButton
                        :modelValue="userRoles"
                        :userid="getUserId"
                        @update:modelValue="onNewRoleAssigned"
                        :assigned="userAssignedRoles"
                        v-if="otherRolesAssignable"
                    />
                </LabelValuePair>
                <Button v-if="hasChangesToSave" @click="handleSaveChanges"> Save Changes </Button>
            </div>
            <RolesAndPermissionListComponent
                :user="getUserId"
                :role="getDefaultRoleId"
                :newrole="userRoles"
                @reset="resetNewRole"
                v-if="getUserId && !isSuperAdmin"
            />
            <div v-else-if="isSuperAdmin">
                <h2 class="text-lg font-light">
                    This user has access to everything as Team Member/Staff
                </h2>
            </div>
        </template>
    </Card>
    <ConfirmUserPermissionChanges
        :changes="getChangesMade"
        :newrole="getNewRoleBeingAssigned"
        @submit="confirmChanges"
        @close="confirmDialog = false"
        v-if="confirmDialog"
    />
</template>
<script>
import { ref, watch } from 'vue';
import LabelValuePair from '@spa/components/LabelValuePair/LabelValuePair.vue';
import Button from '@spa/components/Buttons/Button.vue';
import Card from '@spa/components/Card/Card.vue';
import RolesAndPermissionListComponent from '@spa/modules/users/roles-and-permission/RolesAndPermissionListComponent.vue';
import UserGroupDropdownButton from '@spa/modules/user-group/UserGroupDropdownButton.vue';
import Chip from '@spa/components/Chip/Chip.vue';
import { has } from 'lodash';
import ConfirmUserPermissionChanges from '@spa/modules/users/roles-and-permission/ConfirmUserPermissionChanges.vue';
import { usePermissionStore } from '@spa/stores/modules/permission/usePermissionStore.js';
import { useUserGroupStore } from '@spa/stores/modules/user-group/useUserGroupStore.js';
import useConfirm from '@spa/services/useConfirm';
import { usePage } from '@inertiajs/vue3';

export default {
    setup() {
        const permissionStore = usePermissionStore();
        const groupStore = useUserGroupStore();
        const confirm = useConfirm();
        const page = usePage();

        const confirmChange = (changes) => {
            if (!changes) {
                return Promise.resolve(true);
            }

            return new Promise((resolve) => {
                confirm.require({
                    message:
                        'There are unsaved changes. Are you sure you want to continue? All changes will be lost if continued.',
                    header: 'Confirmation',
                    icon: 'pi pi-exclamation-triangle',
                    accept: () => resolve(true),
                    reject: () => resolve(false),
                    onHide: () => resolve(false),
                });
            });
        };
        return {
            page,
            permissionStore,
            groupStore,
            confirmChange,
        };
    },
    props: {
        store: Object,
    },
    data() {
        return {
            userRoles: null,
            loadgrid: false,
            assignNewRole: false,
            confirmDialog: false,
        };
    },
    components: {
        LabelValuePair,
        Button,
        Card,
        RolesAndPermissionListComponent,
        UserGroupDropdownButton,
        Chip,
        ConfirmUserPermissionChanges,
    },
    mounted() {
        this.loadgrid = true;
    },
    computed: {
        userAssignedRoles() {
            return this.store.formData?.roles || [];
        },
        getDefaultRole() {
            const defaultRole = this.userAssignedRoles.find((item) => item.is_current === true);
            if (defaultRole === undefined && this.userAssignedRoles.length > 0) {
                return this.userAssignedRoles[0];
            }
            return defaultRole;
        },
        getUserId() {
            return this.store.formData?.secure_user_id || null;
        },
        getDefaultRoleId() {
            return this.getDefaultRole?.secure_id || this.getDefaultRole?.id || null;
        },
        getChangesMade() {
            return this.permissionStore.changedPermissions;
            // const changed = this.permissionStore.all.filter((item) => {
            //     return Number(item.permission_status.s) !== Number(item.permission_status.o);
            // });
            //return changed;
        },
        hasChangesToSave() {
            return this.getChangesMade.length > 0 || this.userRoles;
        },
        getNewRoleBeingAssigned() {
            return this.groupStore.all.find((item) => item.id === this.userRoles);
        },
        getUserInfo() {
            return {
                user: this.getUserId,
                role: this.userRoles
                    ? this.getNewRoleBeingAssigned?.secure_id
                    : this.getDefaultRoleId,
                newrole: this.userRoles > 0,
            };
        },
        otherRolesAssignable() {
            if (this.userAssignedRoles.length > 0) {
                const permanentRole = this.userAssignedRoles.find((role) => role.is_permanent);
                return permanentRole === undefined;
            }
            return true;
        },
        isSuperAdmin() {
            if (this.userAssignedRoles.length > 0) {
                const permanentRole = this.userAssignedRoles.find((role) => role.is_master_admin);
                return !!permanentRole;
            }
            return false;
        },
    },
    methods: {
        getClassForActive(role, index) {
            let isActive = false;
            let hasActive = true;
            const activeClass =
                ' bg-green-50 border-green-200 text-green-700 shadow-md hover:bg-green-100';
            const inactiveClass =
                ' hover:shadow-md hover:border-green-200 hover:bg-green-50 hover:text-green-700';
            //check if this is active
            if (!this.userRoles && role.is_current === true) {
                return activeClass;
            } else if (role.is_current === false) {
                return inactiveClass;
            }
            if (
                index === 0 &&
                !this.userAssignedRoles.find((item) => item.is_current === true)?.length
            ) {
                return activeClass;
            }
            return inactiveClass;
        },
        async setCurrentRole(role) {
            if (!this.userRoles && role.is_current === true) {
                return;
            }
            const contd = await this.confirmChange(this.getChangesMade.length > 0);
            if (contd) {
                this.store.formData?.roles.forEach((item) => {
                    item.is_current = item.id === role.id;
                });
                this.userRoles = null;
                this.permissionStore.resetChangeLog();
            }
            return;
        },
        resetNewRole() {
            this.userRoles = null;
        },
        handleSaveChanges() {
            if (this.hasChangesToSave) {
                this.confirmDialog = true;
            }
            return false;
        },
        async confirmChanges() {
            const saveData = {
                user: this.getUserInfo,
                permissions: this.getChangesMade,
            };
            const saved = await this.permissionStore.updatePermissionsInBulk(saveData);
            if (saved) {
                this.confirmDialog = false;
                this.store.formData.roles = saved.roles;
            }
        },
        async onNewRoleAssigned(newVal) {
            const contd = await this.confirmChange(this.getChangesMade.length > 0);
            if (contd) {
                this.userRoles = newVal;
                return;
            }
        },
        removeRole(role) {
            console.log(role);
        },
    },
    watch: {},
};
</script>
