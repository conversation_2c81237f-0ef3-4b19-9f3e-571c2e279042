<?php

use Support\Auth\Access;

return [
    'forms' => [
        'organization' => [
            'label' => 'Organization Info',
            'icon' => '<svg viewBox="0 0 34 34"
                fill="none"
                xmlns="http://www.w3.org/2000/svg">
                    <path d="M24 12.5C24 11.1193 22.8807 10 21.5 10H12.5C11.1193 10 10 11.1193 10 12.5V13H24V12.5ZM24 16.5997V14H10V21.5C10 22.8807 11.1193 24 12.5 24H16.5997C16.2163 23.2499 16 22.4002 16 21.5C16 18.4624 18.4624 16 21.5 16C22.4002 16 23.2499 16.2163 24 16.5997ZM21.5 26C23.9853 26 26 23.9853 26 21.5C26 19.0147 23.9853 17 21.5 17C19.0147 17 17 19.0147 17 21.5C17 23.9853 19.0147 26 21.5 26ZM21 19.5C21 19.2239 21.2239 19 21.5 19C21.7761 19 22 19.2239 22 19.5V21H23C23.2761 21 23.5 21.2239 23.5 21.5C23.5 21.7761 23.2761 22 23 22H21.5C21.2239 22 21 21.7761 21 21.5V19.5Z"
                 fill="#1890FF"></path>
            </svg>',
            'bg' => 'bg-blue-50',
            'children' => [
                'general_info' => ['label' => 'General Info', 'required' => true, 'route' => 'general-info', 'permission' => Access::GENERAL_INFO_ACCESS->value],
                'avetmiss_training_organization_identifier' => ['label' => 'AVETMISS Training Organization Identifier', 'required' => true, 'route' => 'training-organisation', 'permission' => Access::AVETMISS_ACCESS->value],
                'address' => ['label' => 'College Address In Australia', 'required' => true, 'route' => 'main-location', 'permission' => Access::OFFER_MAIN_LOCATION_ACCESS->value],
                'bank_details' => ['label' => 'Bank Details', 'required' => true, 'route' => 'bank-details', 'permission' => Access::BANK_DETAILS_ACCESS->value],
                'usi-setup' => ['label' => 'USI Setup', 'required' => true, 'route' => 'tenant.usi-setups', 'status' => 1, 'permission' => Access::USI_ACCESS->value],
            ],
        ],
        'vet' => [
            'label' => 'VET Study Loans',
            'icon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12.5556 3.55556V4.15625C12.8507 4.19792 13.1354 4.26389 13.3924 4.33333C13.6875 4.41319 13.8646 4.71528 13.7847 5.01389C13.7049 5.3125 13.4028 5.48611 13.1042 5.40625C12.7188 5.30208 12.3403 5.22569 11.9896 5.22222C11.6979 5.21875 11.3889 5.28472 11.1701 5.41319C10.9722 5.53125 10.8889 5.66667 10.8889 5.85764C10.8889 5.98611 10.934 6.08333 11.1424 6.20833C11.3819 6.35069 11.7188 6.45486 12.1563 6.58681L12.1736 6.59028C12.566 6.70833 13.0521 6.85417 13.434 7.09722C13.8542 7.36111 14.2118 7.78125 14.2222 8.42361C14.2326 9.09375 13.8889 9.57986 13.4271 9.86806C13.1597 10.0347 12.8576 10.1319 12.5556 10.184V10.7778C12.5556 11.0833 12.3056 11.3333 12 11.3333C11.6944 11.3333 11.4444 11.0833 11.4444 10.7778V10.1597C11.0556 10.0868 10.691 9.96181 10.3715 9.85069C10.2986 9.82639 10.2257 9.80208 10.1562 9.77778C9.86458 9.68056 9.70833 9.36458 9.80556 9.07639C9.90278 8.78819 10.2188 8.62847 10.5069 8.72569C10.5938 8.75347 10.6736 8.78125 10.7535 8.80903C11.2257 8.96875 11.6076 9.10069 12.0139 9.11111C12.3299 9.12153 12.6354 9.05208 12.8368 8.92708C13.0139 8.81597 13.1111 8.67361 13.1076 8.44097C13.1042 8.28125 13.0451 8.17014 12.8403 8.0382C12.6042 7.88889 12.2674 7.78125 11.8333 7.64931L11.7778 7.63194C11.3958 7.51736 10.934 7.37847 10.5694 7.15625C10.1528 6.90625 9.78472 6.5 9.78125 5.86111C9.77778 5.19097 10.1562 4.72569 10.6076 4.45833C10.8681 4.30556 11.1562 4.20833 11.4444 4.15625V3.55556C11.4444 3.25 11.6944 3 12 3C12.3056 3 12.5556 3.25 12.5556 3.55556ZM7.24306 13.7431C7.69792 13.4375 8.23611 13.2778 8.78472 13.2778H13.9444C15.0174 13.2778 15.8889 14.1493 15.8889 15.2222C15.8889 15.5208 15.8229 15.8021 15.7014 16.0556H15.8958L19.184 14.0972C19.4722 13.9271 19.8021 13.8333 20.1389 13.8333H20.184C21.1875 13.8333 22 14.6458 22 15.6493C22 16.2639 21.6875 16.8368 21.1736 17.1701L17.0208 19.8715C16.3889 20.2812 15.6528 20.5 14.9028 20.5H2.55556C2.25 20.5 2 20.25 2 19.9444C2 19.6389 2.25 19.3889 2.55556 19.3889H14.8993C15.4375 19.3889 15.9618 19.2326 16.4132 18.941L20.566 16.2396C20.7674 16.1111 20.8854 15.8854 20.8854 15.6493C20.8854 15.2604 20.5694 14.9444 20.1806 14.9444H20.1354C20 14.9444 19.8681 14.9826 19.75 15.0486L16.3299 17.0868C16.2431 17.1389 16.1458 17.1667 16.0451 17.1667H13.9444H13.1111H10.8889C10.5833 17.1667 10.3333 16.9167 10.3333 16.6111C10.3333 16.3056 10.5833 16.0556 10.8889 16.0556H13.1111H13.9444C14.4063 16.0556 14.7778 15.684 14.7778 15.2222C14.7778 14.7604 14.4063 14.3889 13.9444 14.3889H8.78472C8.45486 14.3889 8.13542 14.4861 7.86111 14.6701L5.08681 16.5174C4.99653 16.5799 4.88889 16.6111 4.77778 16.6111H2.55556C2.25 16.6111 2 16.3611 2 16.0556C2 15.75 2.25 15.5 2.55556 15.5H4.61111L7.24306 13.7431Z" fill="#1890FF"></path>
            </svg>',
            'bg' => 'bg-blue-50',
            'children' => [
                'vet_study_loans' => ['label' => 'VET Study Loans (VSL) Info', 'required' => true, 'route' => 'vsl-info', 'status' => 1, 'permission' => Access::VSL_INFO_ACCESS->value],
            ],
        ],
        'courses' => [
            'label' => 'Courses',
            'icon' => '<svg width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 15.1375C9.49862 15.6686 8.78802 16 8 16H1.75C0.783502 16 0 15.2165 0 14.25V1.75C0 0.783502 0.783502 0 1.75 0H8C8.78802 0 9.49862 0.331446 10 0.862533C10.5014 0.331446 11.212 0 12 0H18.25C19.2165 0 20 0.783501 20 1.75V14.25C20 15.2165 19.2165 16 18.25 16H12C11.212 16 10.5014 15.6686 10 15.1375ZM1.5 1.75V14.25C1.5 14.3881 1.61193 14.5 1.75 14.5H8C8.69036 14.5 9.25 13.9404 9.25 13.25V2.75C9.25 2.05964 8.69036 1.5 8 1.5H1.75C1.61193 1.5 1.5 1.61193 1.5 1.75ZM10.75 13.25C10.75 13.9404 11.3096 14.5 12 14.5H18.25C18.3881 14.5 18.5 14.3881 18.5 14.25V1.75C18.5 1.61193 18.3881 1.5 18.25 1.5H12C11.3096 1.5 10.75 2.05964 10.75 2.75V13.25Z" fill="#F59E0B"/>
            </svg>',
            'bg' => 'bg-orange-50',
            'children' => [
                'cvr' => ['label' => 'Campus, Venues & Rooms', 'required' => true, 'route' => 'cvr-info', 'status' => 0, 'permission' => Access::CAMPUS_VENUES_ROOMS_ACCESS->value],
                'course_type' => ['label' => 'Course Type', 'required' => true, 'route' => 'course-types', 'status' => 1, 'permission' => Access::COURSE_TYPE_ACCESS->value],
                'manage_courses' => ['label' => 'Manage Courses (Legacy) ', 'required' => true, 'route' => 'courses'/* 'status' => 1 */, 'permission' => Access::MANAGE_COURSES_ACCESS->value],
                'manage_courses_beta' => ['label' => 'Manage Courses Beta', 'required' => true, 'route' => 'spa.courses.index', 'status' => 1, 'permission' => Access::COURSES_ACCESS->value],
                'course_template' => ['label' => 'Course Template', 'required' => true, 'route' => 'course-template', 'permission' => Access::COURSE_TEMPLATE_ACCESS->value],
                'course_upfront_fee' => ['label' => 'Course Upfront Fee', 'required' => true, 'route' => 'courses-upfront-fee'/* 'status' => 1 */, 'permission' => Access::COURSE_UPFRONT_FEE_ACCESS->value],
                'course_promotion' => ['label' => 'Course Promotions', 'required' => true, 'route' => 'promotion-price'/* 'status' => 1 */, 'permission' => Access::COURSE_PROMOTION_ACCESS->value],
                'elicos_discount_week' => ['label' => 'ELICOS Discount Week', 'required' => true, 'route' => 'view-elicos-discount', 'permission' => Access::ELICOS_DISCOUNT_WEEK_ACCESS->value],
                'intake_dates' => ['label' => 'Intake Dates', 'required' => true, 'route' => 'courses-intake-date', 'permission' => Access::INTAKE_DATES_ACCESS->value],
                'training_plan_template' => ['label' => 'Training Plan Template', 'required' => false, 'route' => 'training-plan-template', 'permission' => Access::TRAINING_PLAN_TEMPLATE_ACCESS->value],
            ],
        ],
        'operation' => [
            'label' => 'Organization Operations',
            'icon' => '<svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0 1.74756C0 1.33335 0.335786 0.997559 0.75 0.997559H17.2541C17.8722 0.997559 18.225 1.70321 17.8541 2.19762L13.6898 7.74878L17.8541 13.2999C18.225 13.7944 17.8722 14.5 17.2541 14.5L1.5 14.4996V19.2494C1.5 19.6291 1.21785 19.9429 0.851771 19.9925L0.75 19.9994C0.370304 19.9994 0.0565091 19.7172 0.00684667 19.3512L0 19.2494V1.74756ZM15.7539 2.49756H1.5V13H15.7539L12.1522 8.19885C11.9522 7.93215 11.9522 7.56541 12.1522 7.29872L15.7539 2.49756Z" fill="#1890FF"/>
            </svg>',
            'bg' => 'bg-blue-50',
            'children' => [
                'intervention-strategy' => ['label' => 'Intervention Strategy (Legacy)', 'required' => true, 'route' => 'intervention-strategy-view', 'status' => 1, 'permission' => Access::INTERVENTION_STRATEGY_ACCESS->value],
                'intervention-setup' => ['label' => 'Intervention Setup Beta', 'required' => true, 'route' => 'spa.intervention-setup', 'status' => 1, 'permission' => Access::ORGANIZATION_OPERATIONS_ACCESS->value],
                'improvement-category' => ['label' => 'Improvement Category', 'required' => true, 'route' => 'spa.improvement-categories', 'status' => 1, 'permission' => Access::ORGANIZATION_OPERATIONS_ACCESS->value],
                //                'intervention_settings' => ['label' => 'Intervention Settings', 'required' => true, 'route' => 'compliance-intervention'],
                'section_setup' => ['label' => 'Section Setup', 'required' => true, 'route' => 'manage-section', 'status' => 1, 'permission' => Access::SECTION_SETUP_ACCESS->value],
                'final_outcome' => ['label' => 'Update Final Outcome', 'required' => true, 'route' => 'bulk-update-result'/* 'status' => 1 */, 'permission' => Access::UPDATE_FINAL_OUTCOME_ACCESS->value],
                'student_course_status' => ['label' => 'Update Student Course Status', 'required' => true, 'route' => 'bulk-completion-update', 'status' => 1, 'permission' => Access::UPDATE_STUDENT_COURSE_STATUS_ACCESS->value],
                'public_holidays' => ['label' => 'Public College Holidays', 'required' => true, 'route' => 'holiday-list', 'status' => 1, 'permission' => Access::PUBLIC_COLLEGE_HOLIDAYS_ACCESS->value],
                'elearning_link' => ['label' => 'Forms', 'required' => true, 'route' => 'elearning-link-list', 'permission' => Access::FORMS_ACCESS->value],
            ],
        ],

        'trainers' => [
            'label' => 'Training',
            'icon' => '<svg width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12.754 7C13.7205 7 14.504 7.7835 14.504 8.75V13.499C14.504 15.9848 12.4888 18 10.003 18C7.51712 18 5.50193 15.9848 5.50193 13.499V8.75C5.50193 7.7835 6.28543 7 7.25193 7H12.754ZM12.754 8.5H7.25193C7.11386 8.5 7.00193 8.61193 7.00193 8.75V13.499C7.00193 15.1564 8.34554 16.5 10.003 16.5C11.6604 16.5 13.004 15.1564 13.004 13.499V8.75C13.004 8.61193 12.8921 8.5 12.754 8.5ZM1.75 7L5.13128 6.99906C4.78791 7.41447 4.56424 7.93246 4.51312 8.50019L1.75 8.5C1.61193 8.5 1.5 8.61193 1.5 8.75V11.9988C1.5 13.3802 2.61984 14.5 4.00124 14.5C4.20123 14.5 4.39574 14.4765 4.58216 14.4322C4.66687 14.9361 4.82156 15.4167 5.03487 15.864C4.70577 15.953 4.35899 16 4.00124 16C1.79142 16 0 14.2086 0 11.9988V8.75C0 7.7835 0.783502 7 1.75 7ZM14.8747 6.99906L18.25 7C19.2165 7 20 7.7835 20 8.75V12C20 14.2091 18.2091 16 16 16C15.6436 16 15.298 15.9534 14.9691 15.8659C15.184 15.4177 15.3388 14.9371 15.425 14.4331C15.6092 14.477 15.8019 14.5 16 14.5C17.3807 14.5 18.5 13.3807 18.5 12V8.75C18.5 8.61193 18.3881 8.5 18.25 8.5L15.4928 8.50019C15.4417 7.93246 15.218 7.41447 14.8747 6.99906ZM10 0C11.6569 0 13 1.34315 13 3C13 4.65685 11.6569 6 10 6C8.34315 6 7 4.65685 7 3C7 1.34315 8.34315 0 10 0ZM16.5 1C17.8807 1 19 2.11929 19 3.5C19 4.88071 17.8807 6 16.5 6C15.1193 6 14 4.88071 14 3.5C14 2.11929 15.1193 1 16.5 1ZM3.5 1C4.88071 1 6 2.11929 6 3.5C6 4.88071 4.88071 6 3.5 6C2.11929 6 1 4.88071 1 3.5C1 2.11929 2.11929 1 3.5 1ZM10 1.5C9.17157 1.5 8.5 2.17157 8.5 3C8.5 3.82843 9.17157 4.5 10 4.5C10.8284 4.5 11.5 3.82843 11.5 3C11.5 2.17157 10.8284 1.5 10 1.5ZM16.5 2.5C15.9477 2.5 15.5 2.94772 15.5 3.5C15.5 4.05228 15.9477 4.5 16.5 4.5C17.0523 4.5 17.5 4.05228 17.5 3.5C17.5 2.94772 17.0523 2.5 16.5 2.5ZM3.5 2.5C2.94772 2.5 2.5 2.94772 2.5 3.5C2.5 4.05228 2.94772 4.5 3.5 4.5C4.05228 4.5 4.5 4.05228 4.5 3.5C4.5 2.94772 4.05228 2.5 3.5 2.5Z" fill="#8B5CF6"/>
            </svg>',
            'bg' => 'bg-purple-50',
            'children' => [
                'manage_contract' => ['label' => 'Manage Contract', 'required' => true, 'route' => 'contract-code', 'permission' => Access::MANAGE_CONTRACT_ACCESS->value],
                'manage_contract_funding_source' => ['label' => 'Manage Contract Schedule', 'required' => true, 'route' => 'contract-funding-source', 'permission' => Access::MANAGE_CONTRACT_SCHEDULE_ACCESS->value],
                'course_site' => ['label' => 'View Course Site', 'required' => true, 'route' => 'course-site', 'permission' => Access::VIEW_COURSE_SITE_ACCESS->value],
            ],
        ],
        'students' => [
            'label' => 'Students',
            'icon' => '<svg width="22" height="18" viewBox="0 0 22 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8.90878 0.69964C10.1832 -0.117327 11.8168 -0.117329 13.0912 0.699639L21.1548 5.86861C21.3689 6.0059 21.4989 6.24238 21.5 6.49677C21.5011 6.75116 21.3732 6.98876 21.1602 7.1279L18 9.19255V14.75C18 14.9122 17.9474 15.0701 17.85 15.2L17.8489 15.2014L17.8477 15.2031L17.8446 15.2071L17.8364 15.2178C17.8321 15.2233 17.8269 15.2298 17.8209 15.2373C17.8179 15.2411 17.8146 15.2452 17.8111 15.2495C17.7904 15.2751 17.7619 15.3095 17.7253 15.3513C17.6522 15.4348 17.5469 15.5483 17.4081 15.6816C17.1305 15.9481 16.7176 16.2948 16.1587 16.6387C15.0359 17.3297 13.3387 18 11 18C8.66127 18 6.96408 17.3297 5.8413 16.6387C5.2824 16.2948 4.86951 15.9481 4.59193 15.6816C4.45308 15.5483 4.34778 15.4348 4.27468 15.3513C4.23204 15.3025 4.1901 15.2531 4.15107 15.2014C4.14956 15.1994 4.15 15.2 4.15 15.2C4.05263 15.0701 4 14.9122 4 14.75V9.19255L2 7.88589V13.25C2 13.6642 1.66421 14 1.25 14C0.835786 14 0.5 13.6642 0.5 13.25V6.49997C0.5 6.22949 0.643177 5.99246 0.857875 5.86052L8.90878 0.69964ZM13.1194 12.3812C11.8317 13.2225 10.1683 13.2225 8.88058 12.3812L5.5 10.1725V14.4687C5.5368 14.5072 5.58034 14.5512 5.63073 14.5996C5.84143 14.8018 6.17072 15.0802 6.62745 15.3612C7.53592 15.9203 8.96373 16.5 11 16.5C13.0363 16.5 14.4641 15.9203 15.3726 15.3612C15.8293 15.0802 16.1586 14.8018 16.3693 14.5996C16.4197 14.5512 16.4632 14.5072 16.5 14.4687V10.1725L13.1194 12.3812ZM12.2817 1.96246C11.5006 1.46173 10.4994 1.46173 9.71829 1.96246L2.63041 6.506L9.701 11.1254C10.4902 11.6411 11.5098 11.6411 12.299 11.1254L19.3696 6.506L12.2817 1.96246Z" fill="#6366F1"/>
            </svg>',
            'bg' => 'bg-indigo-50',
            'children' => [
                'oversees_student_health_coverage' => ['label' => 'Overseas Student Health Coverage', 'required' => true, 'route' => 'oshc-infos'/* 'status' => 1 */, 'permission' => Access::OVERSEAS_STUDENT_HEALTH_COVERAGE_ACCESS->value],
                'student_services_information' => ['label' => 'Student Services Information', 'required' => true, 'route' => 'added-services-fee-list'/* 'status' => 1 */, 'permission' => Access::STUDENT_SERVICES_INFORMATION_ACCESS->value],
                'student_id_format' => ['label' => 'Student Id Format', 'required' => true, 'route' => 'student-id-formate'/* 'status' => 1 */, 'permission' => Access::STUDENT_ID_FORMAT_ACCESS->value],
                'student_id_format_beta' => ['label' => 'Student Id Format Beta', 'required' => true, 'route' => 'student-id-formate-beta'/* 'status' => 1 */, 'permission' => Access::STUDENT_ID_FORMAT_ACCESS->value],
                'student_id_templates' => ['label' => 'Student Card Templates', 'required' => true, 'route' => 'spa.student-id-builder.list', 'status' => 1, 'permission' => Access::CERTIFICATE_ID_FORMAT_ACCESS->value],
            ],
        ],
        'agents' => [
            'label' => 'Agents',
            'icon' => '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8.12529 11.9951C7.78221 12.4104 7.55875 12.9281 7.50765 13.4956L2.24888 13.4953C1.83528 13.4953 1.5 13.8306 1.5 14.2442V14.8219C1.5 15.3575 1.69111 15.8756 2.03897 16.283C3.2164 17.6618 5.02273 18.4035 7.5076 18.4883C7.55691 19.0584 7.7808 19.5787 8.12529 19.9957L7.99646 19.9964C4.8506 19.9964 2.46458 19.0913 0.89828 17.257C0.318518 16.5781 0 15.7146 0 14.8219V14.2442C0 13.0539 0.924699 12.0796 2.0949 12.0005L2.24888 11.9953L8.12529 11.9951ZM12.7465 9.49538H15.7465C16.3937 9.49538 16.926 9.98725 16.99 10.6176L16.9965 10.7454L16.996 11.995L18.2465 11.9954C19.213 11.9954 19.9965 12.7789 19.9965 13.7454V18.2454C19.9965 19.2119 19.213 19.9954 18.2465 19.9954H10.2465C9.27996 19.9954 8.49646 19.2119 8.49646 18.2454V13.7454C8.49646 12.7789 9.27996 11.9954 10.2465 11.9954L11.496 11.995L11.4965 10.7454C11.4965 10.0982 11.9883 9.56584 12.6187 9.50183L12.7465 9.49538H15.7465H12.7465ZM18.2465 13.4954H10.2465C10.1084 13.4954 9.99646 13.6073 9.99646 13.7454V18.2454C9.99646 18.3834 10.1084 18.4954 10.2465 18.4954H18.2465C18.3845 18.4954 18.4965 18.3834 18.4965 18.2454V13.7454C18.4965 13.6073 18.3845 13.4954 18.2465 13.4954ZM15.4965 10.9954H12.9965L12.996 11.995H15.496L15.4965 10.9954ZM7.99646 0C10.7579 0 12.9965 2.23858 12.9965 5C12.9965 7.76142 10.7579 10 7.99646 10C5.23503 10 2.99646 7.76142 2.99646 5C2.99646 2.23858 5.23503 0 7.99646 0ZM7.99646 1.5C6.06346 1.5 4.49646 3.067 4.49646 5C4.49646 6.933 6.06346 8.5 7.99646 8.5C9.92945 8.5 11.4965 6.933 11.4965 5C11.4965 3.067 9.92945 1.5 7.99646 1.5Z" fill="#1890FF"/>
            </svg>',
            'bg' => 'bg-primary-blue-50',
            'children' => [
                'agent_document_checklist' => ['label' => 'Agent Document Checklist', 'required' => true, 'route' => 'agent-document'/* 'status' => 1 */, 'permission' => Access::AGENT_DOCUMENT_CHECKLIST_ACCESS->value],
                'agent_status' => ['label' => 'Agent Status', 'required' => true, 'route' => 'agent-status-view', 'status' => 1, 'permission' => Access::AGENT_STATUS_ACCESS->value],
            ],
        ],
        'applications' => [
            'label' => 'Applications',
            'icon' => '<svg width="16" height="20" viewBox="0 0 16 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3 10.25C3 9.83579 3.33579 9.5 3.75 9.5C4.16421 9.5 4.5 9.83579 4.5 10.25C4.5 10.6642 4.16421 11 3.75 11C3.33579 11 3 10.6642 3 10.25ZM3.75 12.5C3.33579 12.5 3 12.8358 3 13.25C3 13.6642 3.33579 14 3.75 14C4.16421 14 4.5 13.6642 4.5 13.25C4.5 12.8358 4.16421 12.5 3.75 12.5ZM3 16.25C3 15.8358 3.33579 15.5 3.75 15.5C4.16421 15.5 4.5 15.8358 4.5 16.25C4.5 16.6642 4.16421 17 3.75 17C3.33579 17 3 16.6642 3 16.25ZM6.75 9.5C6.33579 9.5 6 9.83579 6 10.25C6 10.6642 6.33579 11 6.75 11H12.25C12.6642 11 13 10.6642 13 10.25C13 9.83579 12.6642 9.5 12.25 9.5H6.75ZM6 13.25C6 12.8358 6.33579 12.5 6.75 12.5H12.25C12.6642 12.5 13 12.8358 13 13.25C13 13.6642 12.6642 14 12.25 14H6.75C6.33579 14 6 13.6642 6 13.25ZM6.75 15.5C6.33579 15.5 6 15.8358 6 16.25C6 16.6642 6.33579 17 6.75 17H12.25C12.6642 17 13 16.6642 13 16.25C13 15.8358 12.6642 15.5 12.25 15.5H6.75ZM15.414 6.414L9.585 0.586C9.57005 0.571048 9.55311 0.55808 9.53628 0.545195C9.52385 0.535674 9.51147 0.526198 9.5 0.516C9.429 0.452 9.359 0.389 9.281 0.336C9.25573 0.318942 9.22806 0.305475 9.20052 0.292071C9.18447 0.284259 9.16846 0.276469 9.153 0.268C9.1363 0.258594 9.11966 0.248966 9.103 0.239326C9.0488 0.20797 8.99436 0.176475 8.937 0.152C8.74 0.0699999 8.528 0.029 8.313 0.0139999C8.2933 0.0127423 8.27377 0.0100789 8.2542 0.00740933C8.22708 0.00371051 8.19988 0 8.172 0H2C0.896 0 0 0.896 0 2V18C0 19.104 0.896 20 2 20H14C15.104 20 16 19.104 16 18V7.828C16 7.298 15.789 6.789 15.414 6.414ZM14.5 18C14.5 18.275 14.276 18.5 14 18.5H2C1.724 18.5 1.5 18.275 1.5 18V2C1.5 1.725 1.724 1.5 2 1.5H8V6C8 7.104 8.896 8 10 8H14.5V18ZM9.5 2.621L13.378 6.5H10C9.724 6.5 9.5 6.275 9.5 6V2.621Z" fill="#F59E0B"/>
            </svg>',
            'bg' => 'bg-yellow-50',
            'onboarding' => false,
            'children' => [
                'country_list' => ['label' => 'Country List', 'required' => true, 'route' => 'country-list', 'status' => 1, 'permission' => Access::COUNTRY_LIST_ACCESS->value],
                'language_list' => ['label' => 'Language List', 'required' => true, 'route' => 'language-list', 'status' => 1, 'permission' => Access::LANGUAGE_LIST_ACCESS->value],
            ],
        ],
        'templates_letters' => [
            'label' => 'Templates/Letters',
            'icon' => '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0 3.25C0 1.45507 1.45507 0 3.25 0H13.75C15.5449 0 17 1.45507 17 3.25V8.00723C16.9173 8.00243 16.8339 8 16.75 8H15.5V3.25C15.5 2.2835 14.7165 1.5 13.75 1.5H3.25C2.2835 1.5 1.5 2.2835 1.5 3.25V14.75C1.5 15.7165 2.2835 16.5 3.25 16.5H4V16.75C4 17.185 4.06536 17.6048 4.1868 18H3.25C1.45508 18 0 16.5449 0 14.75V3.25ZM4.75 3C4.33579 3 4 3.33579 4 3.75C4 4.16421 4.33579 4.5 4.75 4.5H12.25C12.6642 4.5 13 4.16421 13 3.75C13 3.33579 12.6642 3 12.25 3H4.75ZM6 6.25C6 5.83579 6.33579 5.5 6.75 5.5H12.25C12.6642 5.5 13 5.83579 13 6.25C13 6.66421 12.6642 7 12.25 7H6.75C6.33579 7 6 6.66421 6 6.25ZM5 12.25C5 10.4551 6.45507 9 8.25 9H16.75C18.5449 9 20 10.4551 20 12.25V16.75C20 18.5449 18.5449 20 16.75 20H8.25C6.45507 20 5 18.5449 5 16.75V12.25ZM8.25 10.5C7.74571 10.5 7.29124 10.7133 6.9719 11.0546L12.5001 14.6084L18.0281 11.0546C17.7088 10.7133 17.2543 10.5 16.75 10.5H8.25ZM6.5 16.75C6.5 17.7165 7.2835 18.5 8.25 18.5H16.75C17.7165 18.5 18.5 17.7165 18.5 16.75V12.5345L12.9056 16.1309C12.6586 16.2897 12.3415 16.2897 12.0945 16.1309L6.5 12.5345V16.75Z" fill="#6366F1"/>
            </svg>',
            'bg' => 'bg-indigo-50',
            'children' => [
                'add_edit_letter' => ['label' => 'Add New or Edit Letter', 'required' => true, 'route' => 'letter-template-list', 'status' => 1, 'permission' => Access::ADD_EDIT_LETTER_ACCESS->value],
                'add_edit_email_template' => ['label' => 'Add/Edit Email Template', 'required' => true, 'route' => 'email-template-list', 'status' => 1, 'permission' => Access::ADD_EDIT_EMAIL_TEMPLATE_ACCESS->value],
                'sms-template-list' => ['label' => 'Add/Edit SMS Template', 'required' => true, 'route' => 'sms-template-list', 'status' => 1, 'permission' => Access::ADD_EDIT_SMS_TEMPLATE_ACCESS->value],
                'letter_setting' => ['label' => 'Letter Setting', 'required' => true, 'route' => 'letter-setting-view'/* 'status' => 1 */, 'permission' => Access::LETTER_SETTING_ACCESS->value],
                'failed-email' => ['label' => 'Failed Emails', 'required' => false, 'route' => 'failed-email', 'status' => 1, 'permission' => Access::FAILED_EMAILS_ACCESS->value],
                'checklist' => ['label' => 'Checklist', 'required' => true, 'route' => 'checklist', 'status' => 1, 'permission' => Access::CHECKLIST_ACCESS->value],
                'failed_email' => ['label' => 'Failed Email', 'required' => true, 'route' => 'failed-email', 'status' => 1, 'permission' => Access::FAILED_EMAILS_ACCESS->value],
            ],
        ],
        'finance' => [
            'label' => 'Finance',
            'icon' => '<svg width="20" height="15" viewBox="0 0 20 15" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8.5 3C6.84315 3 5.5 4.34315 5.5 6C5.5 7.65685 6.84315 9 8.5 9C10.1569 9 11.5 7.65685 11.5 6C11.5 4.34315 10.1569 3 8.5 3ZM7 6C7 5.17157 7.67157 4.5 8.5 4.5C9.32843 4.5 10 5.17157 10 6C10 6.82843 9.32843 7.5 8.5 7.5C7.67157 7.5 7 6.82843 7 6ZM0 2.25C0 1.00736 1.00736 0 2.25 0H14.75C15.9926 0 17 1.00736 17 2.25V9.75C17 10.9926 15.9926 12 14.75 12H2.25C1.00736 12 0 10.9926 0 9.75V2.25ZM2.25 1.5C1.83579 1.5 1.5 1.83579 1.5 2.25V3H2.25C2.66421 3 3 2.66421 3 2.25V1.5H2.25ZM1.5 7.5H2.25C3.49264 7.5 4.5 8.50736 4.5 9.75V10.5H12.5V9.75C12.5 8.50736 13.5074 7.5 14.75 7.5H15.5V4.5H14.75C13.5074 4.5 12.5 3.49264 12.5 2.25V1.5H4.5V2.25C4.5 3.49264 3.49264 4.5 2.25 4.5H1.5V7.5ZM15.5 3V2.25C15.5 1.83579 15.1642 1.5 14.75 1.5H14V2.25C14 2.66421 14.3358 3 14.75 3H15.5ZM15.5 9H14.75C14.3358 9 14 9.33579 14 9.75V10.5H14.75C15.1642 10.5 15.5 10.1642 15.5 9.75V9ZM1.5 9.75C1.5 10.1642 1.83579 10.5 2.25 10.5H3V9.75C3 9.33579 2.66421 9 2.25 9H1.5V9.75ZM2.40137 13.5C2.92008 14.3967 3.8896 15 5.00002 15H15.25C17.8734 15 20 12.8734 20 10.25V5.00002C20 3.8896 19.3967 2.92008 18.5 2.40137V10.25C18.5 12.0449 17.0449 13.5 15.25 13.5H2.40137Z" fill="#10B981"/>
            </svg>',
            'bg' => 'bg-green-50',
            'children' => [
                'enrollment_fee' => ['label' => 'Enrollment Fee', 'required' => true, 'route' => 'enrollment-fees'/* 'status' => 1 */, 'permission' => Access::ENROLLMENT_FEE_ACCESS->value],
                'reconcile_bank' => ['label' => 'Reconcile Bank (Legacy)', 'required' => true, 'route' => 'bank-reconciliation', 'permission' => Access::RECONCILE_BANK_ACCESS->value],
                'reconcile_bank_beta' => ['label' => 'Reconcile Bank', 'required' => true, 'route' => 'spa.bank-reconciliation', 'permission' => Access::RECONCILE_BANK_BETA_ACCESS->value],
                'setup_account_payment' => ['label' => 'Setup Account Payment', 'required' => true, 'route' => 'student-account-setup', 'permission' => Access::SETUP_ACCOUNT_PAYMENT_ACCESS->value],
                'manage_bank_information' => ['label' => 'Manage Bank Information', 'required' => true, 'route' => 'bank-list', 'permission' => Access::BANK_DETAILS_ACCESS->value],
                'invoice_settings' => ['label' => 'Invoice Settings', 'required' => true, 'route' => 'invoice-settings', 'permission' => Access::INVOICE_SETTINGS_ACCESS->value],
                'schedule_type' => ['label' => 'Schedule Type', 'required' => true, 'route' => 'schedule-type', 'permission' => Access::MANAGE_CONTRACT_SCHEDULE_ACCESS->value],
                'tenant_billing' => ['label' => 'Tenant Billing', 'required' => true, 'route' => 'tenant.billing', 'status' => 1, 'permission' => Access::BILLING_ACCESS->value],
            ],
        ],
        'data_reporting' => [
            'label' => 'Data Reporting',
            'icon' => '<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M2 1.25C2 0.835786 1.66421 0.5 1.25 0.5C0.835786 0.5 0.5 0.835786 0.5 1.25V15.75C0.5 16.7165 1.2835 17.5 2.25 17.5H16.75C17.1642 17.5 17.5 17.1642 17.5 16.75C17.5 16.3358 17.1642 16 16.75 16H2.25C2.11193 16 2 15.8881 2 15.75V1.25ZM11 3.75C11 3.33579 11.3358 3 11.75 3H16.7515C17.1657 3 17.5015 3.33578 17.5015 3.74999L17.5015 8.75498C17.5015 9.16919 17.1658 9.50498 16.7515 9.50499C16.3373 9.50499 16.0015 9.16921 16.0015 8.755L16.0015 5.55917L10.7803 10.7803C10.4874 11.0732 10.0126 11.0732 9.71967 10.7803L7.75 8.81066L4.53033 12.0303C4.23744 12.3232 3.76256 12.3232 3.46967 12.0303C3.17678 11.7374 3.17678 11.2626 3.46967 10.9697L7.21967 7.21967C7.51256 6.92678 7.98744 6.92678 8.28033 7.21967L10.25 9.18934L14.9393 4.5H11.75C11.3358 4.5 11 4.16421 11 3.75Z" fill="#EC4899"/>
            </svg>',
            'bg' => 'bg-pink-50',
            'children' => [
                'reports' => ['label' => 'Reports', 'required' => false, 'route' => 'manage-report', 'status' => 1, 'permission' => Access::REPORTS_ACCESS->value],
                'validate_prisms' => ['label' => 'Validate PRIMSMS', 'required' => false, 'route' => 'prisms-data-validation', 'permission' => Access::VALIDATE_PRISMS_ACCESS->value],
                'survey_management' => ['label' => 'Survey Management', 'required' => false, 'route' => 'survey-manager-question-management', 'permission' => Access::SURVEY_MANAGEMENT_ACCESS->value],
                'avetmiss' => ['label' => 'AVETMISS', 'required' => false, 'route' => 'student-avet-miss-data-exports', 'status' => 1, 'permission' => Access::AVETMISS_ACCESS->value],
                'vsn' => ['label' => 'VSN', 'required' => false, 'route' => 'general-info', 'permission' => Access::GENERAL_INFO_ACCESS->value],
                'nvr' => ['label' => 'NVR', 'required' => false, 'route' => 'nvr-report-data-extraction', 'permission' => Access::NVR_ACCESS->value],
                'tcsi_report' => ['label' => 'TCSI Report', 'required' => false, 'route' => 'tcsi-report', 'permission' => Access::REPORTS_ACCESS->value],
            ],
        ],
        'higher_ed_reporting' => [
            'label' => 'Higher Ed Reporting',
            'icon' => '<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M2 1.25C2 0.835786 1.66421 0.5 1.25 0.5C0.835786 0.5 0.5 0.835786 0.5 1.25V15.75C0.5 16.7165 1.2835 17.5 2.25 17.5H16.75C17.1642 17.5 17.5 17.1642 17.5 16.75C17.5 16.3358 17.1642 16 16.75 16H2.25C2.11193 16 2 15.8881 2 15.75V1.25ZM11 3.75C11 3.33579 11.3358 3 11.75 3H16.7515C17.1657 3 17.5015 3.33578 17.5015 3.74999L17.5015 8.75498C17.5015 9.16919 17.1658 9.50498 16.7515 9.50499C16.3373 9.50499 16.0015 9.16921 16.0015 8.755L16.0015 5.55917L10.7803 10.7803C10.4874 11.0732 10.0126 11.0732 9.71967 10.7803L7.75 8.81066L4.53033 12.0303C4.23744 12.3232 3.76256 12.3232 3.46967 12.0303C3.17678 11.7374 3.17678 11.2626 3.46967 10.9697L7.21967 7.21967C7.51256 6.92678 7.98744 6.92678 8.28033 7.21967L10.25 9.18934L14.9393 4.5H11.75C11.3358 4.5 11 4.16421 11 3.75Z" fill="#EC4899"/>
            </svg>',
            'bg' => 'bg-pink-50',
            'children' => [
                'tcsi_report' => ['label' => 'TCSI Report', 'required' => true, 'route' => 'tcsi-report', 'status' => 1, 'permission' => Access::REPORTS_ACCESS->value],
                'credit_transfer_report' => ['label' => 'Credit Transfer Report', 'required' => true, 'route' => 'spa.credit-transfer-report', 'status' => 1, 'permission' => Access::REPORTS_ACCESS->value],
            ],
        ],
        'other' => [
            'label' => 'Other',
            'icon' => '<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M2 1.25C2 0.835786 1.66421 0.5 1.25 0.5C0.835786 0.5 0.5 0.835786 0.5 1.25V15.75C0.5 16.7165 1.2835 17.5 2.25 17.5H16.75C17.1642 17.5 17.5 17.1642 17.5 16.75C17.5 16.3358 17.1642 16 16.75 16H2.25C2.11193 16 2 15.8881 2 15.75V1.25ZM11 3.75C11 3.33579 11.3358 3 11.75 3H16.7515C17.1657 3 17.5015 3.33578 17.5015 3.74999L17.5015 8.75498C17.5015 9.16919 17.1658 9.50498 16.7515 9.50499C16.3373 9.50499 16.0015 9.16921 16.0015 8.755L16.0015 5.55917L10.7803 10.7803C10.4874 11.0732 10.0126 11.0732 9.71967 10.7803L7.75 8.81066L4.53033 12.0303C4.23744 12.3232 3.76256 12.3232 3.46967 12.0303C3.17678 11.7374 3.17678 11.2626 3.46967 10.9697L7.21967 7.21967C7.51256 6.92678 7.98744 6.92678 8.28033 7.21967L10.25 9.18934L14.9393 4.5H11.75C11.3358 4.5 11 4.16421 11 3.75Z" fill="#EC4899"/>
            </svg>',
            'bg' => 'bg-pink-50',
            'children' => [
                'competency-result-grade' => ['label' => 'Competency Grade', 'required' => true, 'route' => 'competency-result-grade', 'status' => 1, 'permission' => Access::COMPETENCY_GRADE_ACCESS->value],
                'placement-provider' => ['label' => 'Placement Provider', 'required' => false, 'route' => 'placement-provider', 'status' => 1, 'permission' => Access::PLACEMENT_PROVIDER_ACCESS->value],
                'smtp-setup' => ['label' => 'SMTP', 'required' => true, 'route' => 'smtp-setup', 'status' => 1, 'permission' => Access::SMTP_SETUP_ACCESS->value],
                'failed-jobs' => ['label' => 'Failed Jobs', 'required' => false, 'route' => 'failed-jobs', 'status' => 1, 'permission' => Access::FAILED_JOBS_ACCESS->value],
                'certificate-id-format-view' => ['label' => 'Certificate Id Format', 'required' => true, 'route' => 'certificate-id-format-view', 'status' => 1, 'permission' => Access::CERTIFICATE_ID_FORMAT_ACCESS->value],
                'certificate-template' => ['label' => 'Certificate Template', 'required' => true, 'route' => 'spa.certificate.templates', 'status' => 1, 'permission' => Access::CERTIFICATE_TEMPLATES_ACCESS->value],
                'assessment-date-extension' => ['label' => 'Extend Assessment Due Date', 'required' => true, 'route' => 'assessment-date-extension', 'status' => 1, 'permission' => Access::EXTEND_ASSESSMENT_DUE_DATE_ACCESS->value],
                'agent-email-template-setting-view' => ['label' => 'Notification Setting', 'required' => false, 'route' => 'agent-email-template-setting-view', 'status' => 1, 'permission' => Access::NOTIFICATION_SETTING_ACCESS->value],
                'offer-labels' => ['label' => 'Offer Labels', 'required' => false, 'route' => 'offer-labels', 'status' => 1, 'permission' => Access::OFFER_LABEL_ACCESS->value],
                'global-queue' => ['label' => 'Global Queue', 'required' => true, 'route' => 'global-queue', 'status' => 1, 'permission' => Access::GLOBAL_QUEUE_ACCESS->value],
                'user-audit-log' => ['label' => 'User Audit', 'required' => true, 'route' => 'user-audit-log', 'status' => 1, 'permission' => null],
                'add-teams-condition' => ['label' => 'Teams and Conditions', 'required' => true, 'route' => 'add-teams-condition', 'status' => 1, 'permission' => null],
                'add-privacy-policy' => ['label' => 'Privacy Policy', 'required' => true, 'route' => 'add-privacy-policy', 'status' => 1, 'permission' => null],
                'declaration' => ['label' => 'Declarations', 'required' => true, 'route' => 'spa.declaration', 'status' => 1, 'permission' => null],
                'credit-provider-code' => ['label' => 'Credit Provider Code', 'required' => true, 'route' => 'spa.tcsi.credit-provider-code', 'status' => 1, 'permission' => null],
            ],
        ],
        'integrations' => [
            'label' => 'Integrations',
            'icon' => '<svg width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="17" cy="17" r="17" fill="#FDF2F8"/>
            <path d="M17 10.5C13.4101 10.5 10.5 13.4101 10.5 17C10.5 17.4142 10.1642 17.75 9.75 17.75C9.33579 17.75 9 17.4142 9 17C9 12.5817 12.5817 9 17 9C21.4183 9 25 12.5817 25 17C25 19.6785 23.6837 22.0485 21.6645 23.5H22.75C23.1642 23.5 23.5 23.8358 23.5 24.25C23.5 24.6642 23.1642 25 22.75 25H19.75C19.3358 25 19 24.6642 19 24.25V21.25C19 20.8358 19.3358 20.5 19.75 20.5C20.1642 20.5 20.5 20.8358 20.5 21.25V22.4784C22.3051 21.3227 23.5 19.3003 23.5 17C23.5 13.4101 20.5899 10.5 17 10.5ZM19.5 17C19.5 18.3807 18.3807 19.5 17 19.5C15.6193 19.5 14.5 18.3807 14.5 17C14.5 15.6193 15.6193 14.5 17 14.5C18.3807 14.5 19.5 15.6193 19.5 17ZM18 17C18 16.4477 17.5523 16 17 16C16.4477 16 16 16.4477 16 17C16 17.5523 16.4477 18 17 18C17.5523 18 18 17.5523 18 17Z" fill="#EC4899"/>
        </svg>
        ',
            'bg' => 'bg-pink-50',
            'children' => [
                'xero-setup' => ['label' => 'Xero',      'required' => env('FEATURE_XERO', false),   'route' => 'xero-setup',            'status' => 1, 'permission' => Access::XERO_SETUP_ACCESS->value],
                'zoho-setup' => ['label' => 'Zoho',      'required' => env('FEATURE_ZOHO', false),   'route' => 'galaxy.zoho.setup',     'status' => 1, 'permission' => Access::ZOHO_SETUP_ACCESS->value],
                'moodle-setup' => ['label' => 'Moodle',    'required' => env('FEATURE_MOODLE', false), 'route' => 'galaxy.moodle.setup',   'status' => 1, 'permission' => Access::MOODLE_SETUP_ACCESS->value],
                'sso-setup' => ['label' => 'Single Sign On (SSO)', 'required' => env('FEATURE_SSO', false), 'route' => 'galaxy.sso.setup', 'status' => 1, 'permission' => Access::SSO_SETUP_ACCESS->value],
                'stripe-setup' => ['label' => 'Stripe Setup', 'required' => env('FEATURE_TENANT_STRIPE', false), 'route' => 'tenant.stripe.setup', 'status' => 1, 'permission' => Access::STRIPE_SETUP_ACCESS->value],
                'shortcourse-setup' => ['label' => 'Short Course Site Setup', 'required' => true, 'route' => 'tenant.shortcourse.setup', 'status' => 1, 'permission' => Access::SHORTCOURSE_SETUP_ACCESS->value],
                'webhook-setup' => ['label' => 'Webhook Setup', 'required' => true, 'route' => 'settings.webhooks.setup', 'status' => 1, 'permission' => Access::WEBHOOK_SETUP_ACCESS->value],
            ],
        ],
        'organization_setup' => [
            'label' => 'Organization Setup',
            'icon' => '<svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="#6CB2EB" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
        </svg>',
            'bg' => 'bg-primary-blue-50',
            'children' => [
                'general-info' => ['label' => 'Organization Setup', 'required' => true, 'route' => 'general-info', 'status' => 1, 'permission' => Access::ORGANISATION_PROFILE_ACCESS->value],
                'staff-list' => ['label' => 'Staff', 'required' => true, 'route' => env('FEATURE_USERS_BETA', false) ? 'spa.manage-users.team-members' : 'staff-list', 'status' => 1, 'permission' => Access::STAFF_LIST_ACCESS->value],
                'manage-user-account' => ['label' => 'Users', 'required' => true, 'route' => env('FEATURE_USERS_BETA', false) ? 'spa.manage-users.team-members' : 'manage-user-account', 'status' => 1, 'permission' => Access::MANAGE_USER_ACCOUNT_ACCESS->value],
                'view-teacher' => ['label' => 'Trainers', 'required' => true, 'route' => env('FEATURE_USERS_BETA', false) ? 'spa.manage-users.trainers' : 'view-teacher', 'status' => 1, 'permission' => Access::USERS_ACCESS->value],
                'view-agent-list' => ['label' => 'Agent', 'required' => true, 'route' => env('FEATURE_USERS_BETA', false) ? 'spa.manage-users.agents' : 'view-agent-list', 'status' => 1, 'permission' => Access::AGENTS_ACCESS->value],
                'manage-employer' => ['label' => 'Employers', 'required' => true, 'route' => env('FEATURE_USERS_BETA', false) ? 'spa.manage-users.employers' : 'manage-employer', 'status' => 1, 'permission' => Access::EMPLOYER_BETA_ACCESS->value],
                'services-setup' => ['label' => 'Service Providers', 'required' => true, 'route' => env('FEATURE_USERS_BETA', false) ? 'spa.manage-users.service-providers' : 'services-setup', 'status' => 1, 'permission' => Access::SERVICE_PROVIDERS_ACCESS->value],
                'Forms' => ['label' => 'Forms', 'required' => true, 'route' => 'elearning-link-list', 'status' => 1, 'permission' => Access::FORMS_ACCESS->value],
            ],
        ],
        'import' => [
            'label' => 'Import',
            'icon' => '<svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="#6CB2EB" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
        </svg>',
            'bg' => 'bg-primary-blue-50',
            'children' => [
                'timetable-import' => ['label' => 'Timetable Import', 'required' => true, 'route' => 'timetable-import', 'status' => 1, 'permission' => Access::TIMETABLES_ACCESS->value],
                'attendance-import' => ['label' => 'Attendance Import', 'required' => true, 'route' => 'spa.attendance-import.index', 'status' => 1, 'permission' => Access::ATTENDANCE_ACCESS->value],
                'result-import' => ['label' => 'Result Import', 'required' => true, 'route' => 'spa.result-import.index', 'status' => 1, 'permission' => Access::ASSESSMENTS_ACCESS->value],
            ],
        ],
        'risk_assessment' => [
            'label' => 'Risk Assessment',
            'icon' => '<svg class="h-6 w-6" fill="#000000" version="1.1" viewBox="144 144 512 512" xmlns="http://www.w3.org/2000/svg">
                <path d="m439.59 197.71 89.773 155.49c7.3789 12.785 7.3789 28.215 0 40.996-7.3789 12.785-20.738 20.5-35.504 20.5h-179.55c-14.762 0-28.125-7.7148-35.504-20.5-7.3789-12.785-7.3789-28.215 0-40.996l89.777-155.49c7.3789-12.785 20.738-20.5 35.504-20.5 14.762 0 28.121 7.7148 35.504 20.5zm182.18 316.77-160.36 99.266c-14.695 9.0469-30.816 11.309-47.934 6.7266l-133-35.641c-2.1367-0.57031-4.4141-0.27344-6.3203 0.82812l-56.672 32.715c-4.625 2.6719-10.574 1.0742-13.246-3.5469l-41.656-72.16c-2.6641-4.6172-1.082-10.562 3.5391-13.234l112.5-65.168c16.969-9.8359 35.344-18.062 54.723-11.781 5.7422 1.8633 12.574 4.2188 19.805 6.7188 24.09 8.3125 54.066 18.668 84.871 25.422 4.7852 1.0508 8.9023 4.0625 11.578 8.4766 2.9531 4.8477 3.8086 10.758 2.3516 16.211-2.8828 10.766-13.98 17.188-24.75 14.316-0.011719-0.003906-0.027344-0.003906-0.03125-0.011718l-102.55-27.48c-4.4453-1.1875-9.0117 1.4414-10.203 5.8828-1.1953 4.4414 1.4492 9.0039 5.8867 10.195l102.55 27.48c23.008 6.1602 41.984 7.2383 65.469-6.4375l85.637-49.883c32.625-18.996 55.156-14.832 62.277-2.9141 5.5078 9.1172 2.2695 23.727-14.469 34.02zm-217.68-157.01c-7.5938 0-13.75 6.1562-13.75 13.75s6.1562 13.75 13.75 13.75 13.75-6.1562 13.75-13.75-6.1562-13.75-13.75-13.75zm0-24.543c5.7734 0 10.516-4.5781 10.727-10.344l2.7148-75.125c0.10938-3.0234-0.90234-5.6641-3-7.8398-2.0977-2.1797-4.7031-3.2812-7.7266-3.2812h-5.4258c-3.0234 0-5.625 1.1055-7.7266 3.2812-2.0977 2.1797-3.1133 4.8164-3 7.8398l2.7148 75.125c0.20703 5.7734 4.9453 10.344 10.723 10.344z" fill-rule="evenodd"/>
                </svg>',
            'bg' => 'bg-blue-50',
            'children' => [
                'risk_assessment_setting' => ['label' => 'Risk Assessment Setting', 'required' => true, 'route' => 'spa.student-risk-assessment-settings', 'status' => 1, 'permission' => Access::RISK_ASSESSMENT_MATRIX_ACCESS->value],
                'risk_assessment_queue' => ['label' => 'Risk Assessment Queue', 'required' => true, 'route' => 'spa.student-risk-assessment-queue', 'status' => 1, 'permission' => Access::RISK_ASSESSMENT_MATRIX_ACCESS->value],
                'risk_assessment_matrix' => ['label' => 'Risk Assessment Matrix', 'required' => true, 'route' => 'spa.student-risk-assessment', 'status' => 1, 'permission' => Access::RISK_ASSESSMENT_MATRIX_ACCESS->value],
            ],
        ],
    ],
];
