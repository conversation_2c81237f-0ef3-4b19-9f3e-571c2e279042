<template lang="">
    <div :class="rootClass">
        <slot name="icon-start" />
        <div class="">{{ label }}</div>
        <slot name="icon-end">
            <icon-dismiss v-if="removable" :class="iconClass" @click="onClick" />
        </slot>
    </div>
</template>
<script>
import { IconDismiss20Regular } from '@iconify-prerendered/vue-fluent';
import { twMerge } from 'tailwind-merge';

export default {
    props: {
        label: String,
        removable: {
            type: Boolean,
            default: true,
        },
        pt: {
            type: Object,
            default: {},
        },
    },
    computed: {
        rootClass() {
            return twMerge(
                'inline-flex items-center gap-2 rounded border border-gray-200 bg-white p-0.5 ps-2 pe-1 text-sm leading-5 text-gray-700',
                this.pt.root
            );
        },
        iconClass() {
            return twMerge('h-3 w-3 stroke-2 text-gray-400 hover:text-gray-600', this.pt.icon);
        },
    },
    components: {
        'icon-dismiss': IconDismiss20Regular,
    },
    methods: {
        onClick(e) {
            this.$emit('remove', e);
        },
    },
};
</script>
<style lang=""></style>
