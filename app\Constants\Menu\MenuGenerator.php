<?php

namespace App\Constants\Menu;

use App\Model\v2\AgentStaff;
use Illuminate\Support\Facades\Session;
use SSO\Services\IntegrationService;

class MenuGenerator
{
    public static $MENU_ITEMS = null;

    public static function GenerateForUser($user)
    {

        // info('menuItems', collect(StudentSpaMenu::STUDENT_MENU_ITEMS)->map(function ($item) {
        //     return [
        //         'label' => $item['label'],
        //         'url' => $item['url'],
        //         'sub_menu' => @$item['sub_menu'],
        //     ];
        // })->toArray());
        // dd($user);
        if (! $user) {
            return;
        }

        if (! is_null(self::$MENU_ITEMS)) {
            return self::$MENU_ITEMS;
        }

        /* FOR CENTRAL LOGIN DIRECTLY RETURN CENTRAL MENU */
        if (authUser('central')) {

            self::$MENU_ITEMS = CentralMenu::CENTRAL_MENU_ITEMS;

            return self::$MENU_ITEMS;
        }

        $permissions = $user->getCurrentActiveRolePermissions();
        if (! $permissions) {
            $permissions = [];
        }
        if ($user->isAcountant()) {
            self::$MENU_ITEMS = self::MapForCurrentRole(AccountMenu::ACCOUNT_MENU_ITEMS, $permissions, '');
        } elseif ($user->isAdmin()) {
            self::$MENU_ITEMS = self::MapForCurrentRole(SadminMenu::SADMIN_MENU_ITEMS, $permissions, '');
        } elseif ($user->isDos()) {
            self::$MENU_ITEMS = self::MapForCurrentRole(SadminMenu::SADMIN_MENU_ITEMS, $permissions, '');
        } elseif ($user->isIT()) {
            self::$MENU_ITEMS = self::MapForCurrentRole(SadminMenu::SADMIN_MENU_ITEMS, $permissions, '');
        } elseif ($user->isMarketing()) {
            self::$MENU_ITEMS = self::MapForCurrentRole(MarketingMenu::MARKETING_MENU_ITEMS, $permissions, '');
        } elseif ($user->isSadmin()) {
            $menuItems = SadminMenu::SADMIN_MENU_ITEMS;

            $qTaskIntegration = IntegrationService::getIntegration('qtask');
            if ($qTaskIntegration) {
                $isActiveOrPaused = $qTaskIntegration->isActive() || $qTaskIntegration->isPaused();
                $ssoUrl = $qTaskIntegration->ssoUrl();

                if ($isActiveOrPaused) {
                    foreach ($menuItems as &$item) {
                        if (isset($item['label']) && $item['label'] === 'Task Management Beta') {
                            $item['url'] = $ssoUrl;
                            break;
                        }
                    }
                    unset($item);

                    $menuItems = array_values(array_filter($menuItems, function ($item) {
                        return ! isset($item['label']) || $item['label'] !== 'Task Management';
                    }));
                } else {
                    $menuItems = array_values(array_filter($menuItems, function ($item) {
                        return ! isset($item['label']) || $item['label'] !== 'Task Management Beta';
                    }));
                }
            } else {
                $menuItems = array_values(array_filter($menuItems, function ($item) {
                    return ! isset($item['label']) || $item['label'] !== 'Task Management Beta';
                }));
            }

            self::$MENU_ITEMS = self::MapForCurrentRole($menuItems, $permissions, '');
        } elseif ($user->isStaff()) {
            self::$MENU_ITEMS = self::MapForCurrentRole(StaffMenu::STAFF_MENU_ITEMS, $permissions, '');
        } elseif ($user->isStudentService()) {
            self::$MENU_ITEMS = self::MapForCurrentRole(StudentServiceMenu::STUDENTSERVICE_MENU_ITEMS, $permissions, 'spp_');
        } elseif ($user->isAgent()) {

            if (galaxy_feature('agent_portal')) {
                if (Session::get('agentStaffRole') == AgentStaff::AGENT_STAFF_STANDARD_USER) {
                    self::$MENU_ITEMS = self::MapForCurrentRole(AgentStaffStandardSpaMenu::AGENT_MENU_ITEMS, $permissions, 'ap_');

                    return self::$MENU_ITEMS;
                } else {
                    self::$MENU_ITEMS = self::MapForCurrentRole(AgentSpaMenu::AGENT_MENU_ITEMS, $permissions, 'ap_');

                    return self::$MENU_ITEMS;
                }
            }

            self::$MENU_ITEMS = self::MapForCurrentRole(AgentMenu::AGENT_MENU_ITEMS, $permissions, 'ap_');
            // dd(self::$MENU_ITEMS);
        } elseif ($user->isTeacher()) {
            if (galaxy_feature('trainer_portal')) {
                self::$MENU_ITEMS = self::MapForCurrentRole(TeacherSpaMenu::TEACHER_MENU_ITEMS, $permissions, 'tp_');

                return self::$MENU_ITEMS;
            }

            self::$MENU_ITEMS = self::MapForCurrentRole(TeacherMenu::TEACHER_MENU_ITEMS, $permissions, 'tp_');
        } elseif ($user->isStudent()) {
            if (galaxy_feature('student_portal_beta')) {
                self::$MENU_ITEMS = self::MapForCurrentRole(StudentSpaMenu::STUDENT_MENU_ITEMS, $permissions, 'sp_');

                return self::$MENU_ITEMS;
            }

            self::$MENU_ITEMS = self::MapForCurrentRole(StudentMenu::STUDENT_MENU_ITEMS, $permissions, 'sp_');
        } elseif ($user->isAgentStaff()) {
            if (galaxy_feature('agent_staff_portal')) {
                self::$MENU_ITEMS = self::MapForCurrentRole(AgentStaffSpaMenu::AGENT_STAFF_MENU_ITEMS, $permissions, 'ap_');

                return self::$MENU_ITEMS;
            }

            self::$MENU_ITEMS = self::MapForCurrentRole(AgentStaffMenu::AGENT_STAFF_MENU_ITEMS, $permissions, 'ap_');
        } else {
        }

        // dd(self::$MENU_ITEMS);

        return self::$MENU_ITEMS ?? [];
    }

    public static function GenerateForPage($page) {}

    public static function MapForCurrentRole($items = [], $permissions = [], $prefix = '')
    {
        // dd($items, $permissions, $prefix);
        if (in_array('all', $permissions)) {
            return $items;
        }

        // dd($items, $permissions, $prefix);

        $finalItems = [];
        foreach ($items as $item) {

            if (! isset($item['sub_menu']) && (! isset($item['permissions']) || empty($item['permissions']))) {

                $finalItems[] = $item;

                continue;
            }

            if (isset($item['sub_menu']) && ! empty($item['sub_menu'])) {

                /* if main parent item permission matches skip submenu iteration */
                if (isset($item['permissions']) && count(array_intersect($item['permissions'], $permissions))) {
                    $finalItems[] = $item;

                    continue;
                }
                /* if this is parent menu and has main access all child items should be automaticaly available */

                /* SO main menu access not found but if we have atleast one submenu we need to show paret mentu */
                $subMenus = self::MapForCurrentRole($item['sub_menu'], $permissions, $prefix);
                if (empty($subMenus)) {
                    continue;
                }
                $item['sub_menu'] = $subMenus;
                $finalItems[] = $item;

                continue;
            }

            /* WE RE */
            if (isset($item['permissions']) && count(array_intersect($item['permissions'], $permissions))) {
                $finalItems[] = $item;

                continue;
            }

            // return collect($items)->filter(function ($item) use ($permissions, $prefix) {

        }

        // dd($finalItems, $items);
        return $finalItems;

        // })->toArray();
        // $rolePermissions = array_map(function ($permission) {
        //     return new Access($permission);
        // }, $rolePermissions);
    }
}
