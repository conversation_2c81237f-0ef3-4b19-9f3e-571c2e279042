<?php

use GalaxyAPI\Controllers\ActivityController;
use GalaxyAPI\Controllers\AgentCommissionController;
use GalaxyAPI\Controllers\AgentCommunicationController;
use GalaxyAPI\Controllers\AgentController;
use GalaxyAPI\Controllers\AgentDocumentChecklistController;
use GalaxyAPI\Controllers\AgentEmailController;
use GalaxyAPI\Controllers\AgentPaymentHistoryController;
use GalaxyAPI\Controllers\AgentStaffController;
use GalaxyAPI\Controllers\AgentStatusController;
use GalaxyAPI\Controllers\AgentStudentController;
use GalaxyAPI\Controllers\AssignSupervisorController;
use GalaxyAPI\Controllers\CertificateAttributeController;
use GalaxyAPI\Controllers\CollegeDetailController;
use GalaxyAPI\Controllers\CollegesController;
use GalaxyAPI\Controllers\CommonUtilityController;
use GalaxyAPI\Controllers\CountryController;
use GalaxyAPI\Controllers\CoursesController;
use GalaxyAPI\Controllers\CourseSubjectController;
use GalaxyAPI\Controllers\CourseSubjectsController;
use GalaxyAPI\Controllers\CourseTypeController;
use GalaxyAPI\Controllers\CreditBonusAllocationController;
use GalaxyAPI\Controllers\CreditTransferReportController;
use GalaxyAPI\Controllers\DepartmentController;
use GalaxyAPI\Controllers\EmailTemplateController;
use GalaxyAPI\Controllers\EmployerController;
use GalaxyAPI\Controllers\ImprovementCategoryController;
use GalaxyAPI\Controllers\IndustriesController;
use GalaxyAPI\Controllers\InterventionStrategyController;
use GalaxyAPI\Controllers\InterventionTypeController;
use GalaxyAPI\Controllers\LeaveController;
use GalaxyAPI\Controllers\PaymentModeController;
use GalaxyAPI\Controllers\PayPeriodController;
use GalaxyAPI\Controllers\PdfTemplateController;
use GalaxyAPI\Controllers\PermissionController;
use GalaxyAPI\Controllers\RegisterImprovementController;
use GalaxyAPI\Controllers\SemesterController;
use GalaxyAPI\Controllers\SetupProviderController;
use GalaxyAPI\Controllers\SetupSectionController;
use GalaxyAPI\Controllers\SetupServiceController;
use GalaxyAPI\Controllers\SetupServicesCategoryController;
use GalaxyAPI\Controllers\SetupServicesNameController;
use GalaxyAPI\Controllers\StaffCommunicationLogController;
use GalaxyAPI\Controllers\StaffController;
use GalaxyAPI\Controllers\StaffEmailController;
use GalaxyAPI\Controllers\StaffPositionController;
use GalaxyAPI\Controllers\StudentAgentCommissionController;
use GalaxyAPI\Controllers\StudentController;
use GalaxyAPI\Controllers\StudentInterventionController;
use GalaxyAPI\Controllers\StudentPaymentAgentCreditController;
use GalaxyAPI\Controllers\StudentRiskAssessmentController;
use GalaxyAPI\Controllers\StudentRiskAssessmentSemesterController;
use GalaxyAPI\Controllers\StudentRiskAssessmentSettingsController;
use GalaxyAPI\Controllers\StudentTrainingActivityLogController;
use GalaxyAPI\Controllers\StudentTrainingController;
use GalaxyAPI\Controllers\SystemRoleController;
use GalaxyAPI\Controllers\TeacherController;
use GalaxyAPI\Controllers\TeacherMatrixController;
use GalaxyAPI\Controllers\TeamMemberController;
use GalaxyAPI\Controllers\TimesheetApprovalController;
use GalaxyAPI\Controllers\TimesheetApprovedController;
use GalaxyAPI\Controllers\TimesheetSubmissionController;
use GalaxyAPI\Controllers\TimetableController;
use GalaxyAPI\Controllers\UserGroupController;
use GalaxyAPI\Controllers\UsersController;
use GalaxyAPI\Controllers\USIVerificationController;
use GalaxyAPI\Controllers\VpmsPlacementProviderController;
use Illuminate\Support\Facades\Route;
use Laravel\Jetstream\Http\Controllers\Inertia\OtherBrowserSessionsController;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;

/*
 * Documentation:
 * Supported routes:
    Route::get('', 'index');
    Route::get('bulk-actions', 'getBulkActions');
    Route::post('bulk-actions', 'handelBulkActions');
    Route::post('', 'store');
    Route::get('{id}', 'show');
    Route::post('{id}', 'update');
    Route::put('{id}/status-change/{column}', 'changeStatusOtherColumn');
    Route::put('{id}/status-change', 'changeStatus');
    Route::delete('{id}', 'destroy');
*/

Route::middleware([PreventAccessFromCentralDomains::class])
    ->group(function () {
        Route::prefix('utilities')
            ->controller(CommonUtilityController::class)
            ->group(function () {
                Route::get('get-enum-options', 'getEnumOptions');
            });
        Route::prefix('countries')
            ->controller(CountryController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::get('{id}', 'show');
            });

        Route::prefix('students')
            ->controller(StudentController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::get('export/usi-students', 'exportUSIStudents');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('{id}', 'update');
            });
        Route::prefix('course-types')
            ->controller(CourseTypeController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::get('{id}', 'show');
            });

        Route::prefix('usi-verifications')
            ->controller(USIVerificationController::class)
            ->group(function () {
                Route::post('verify', 'verify');
                Route::post('verify-bulk', 'verifyBulk');
            });

        Route::prefix('pdf-templates')
            ->controller(PdfTemplateController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('{id}', 'update');
                Route::post('{id}/update-default', 'updateDefaultTemplate');
            });

        Route::prefix('register-improvement')->controller(RegisterImprovementController::class)->group(function () {
            Route::get('', 'index');
            Route::get('categories', 'getCategories');
            Route::get('{id}', 'show');
            Route::post('', 'store');
            Route::post('delete', 'delete');
            Route::post('{id}', 'update');
            Route::delete('{id}', 'destroy');
        });

        Route::prefix('improvement-category')->controller(ImprovementCategoryController::class)->group(function () {
            Route::get('', 'index');
            Route::post('', 'store');
            Route::get('{id}', 'show');
            Route::post('{id}', 'update');
            Route::delete('{id}', 'destroy');
        });

        Route::prefix('staff')
            ->controller(StaffController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('import', 'importStaff');
                Route::get('import/sample', 'downloadSampleTemplate');
                Route::get('{id}/steps', 'getSteps');
                Route::get('{id}', 'show');
                Route::post('save-step', 'saveStep');
                Route::post('bulk-create', 'bulkCreate');
                Route::post('export', 'export');
                Route::post('', 'store');
                Route::post('{id}', 'update');
                Route::post('{id}/update-code', 'updateStaffCode')->name('tenant.staff.update-code');
                Route::delete('{id}', 'destroy');
                Route::put('{id}/restore', 'restoreTrashed');
            });

        Route::prefix('team-member')->controller(TeamMemberController::class)->group(function () {
            Route::get('', 'index');
            Route::post('', 'store');
            Route::get('{id}', 'show');
            Route::post('{id}', 'update');
            Route::delete('{id}', 'destroy');
        });

        Route::prefix('staff-position')
            ->controller(StaffPositionController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('{id}', 'update');
                Route::delete('{id}', 'destroy');
            });

        Route::prefix('staff-communication-log')
            ->controller(StaffCommunicationLogController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::delete('{id}', 'destroy');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
                Route::delete('{id}', 'destroy');
            });

        Route::prefix('staff-email')->controller(StaffEmailController::class)->group(function () {
            Route::get('', 'index');
            Route::post('', 'store');
            Route::get('get-college-email', 'getCollegeEmail');
            Route::get('{id}', 'show');
            Route::post('{id}', 'update');
        });

        Route::prefix('teacher')
            ->controller(TeacherController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::get('{id}/document', 'getDocument');
                Route::get('{id}/downloadFile/{fileId}', 'downloadFile');
                Route::post('delete', 'delete');
                Route::post('export', 'export');
                Route::post('{id}', 'update');
                Route::post('{id}/update-code', 'updateTeacherCode')->name('tenant.teacher.update-code');
                Route::post('{id}/user-id', 'updateUserId');
                Route::post('{id}/create-folder', 'createFolder');
                Route::post('{id}/upload-documents', 'uploadDocuments');
                Route::post('{id}/delete', 'deleteDocument');
                Route::post('{id}/move', 'moveDocument');
                Route::post('{id}/bookmark', 'bookmarkDocument')->name('spa.teacher.bookmarkDocument');
                Route::post('{id}/search', 'searchDocument');
                Route::put('{id}/rename', 'renameFileFolder');
                Route::put('{id}/restore', 'restoreTrashed');
                Route::delete('{id}', 'destroy');
            });

        Route::prefix('teacher-matrix')
            ->controller(TeacherMatrixController::class)->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::delete('{id}', 'destroy');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
                Route::delete('{id}', 'destroy');
            });

        Route::prefix('pay-period')
            ->controller(PayPeriodController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::get('financial-years', 'financialYears');
                Route::get('form-constants', 'formConstants');
                Route::post('', 'store');
                Route::delete('{id}', 'destroy');
                Route::post('delete', 'delete');
                Route::delete('{id}', 'destroy');
            });

        Route::prefix('assign-supervisor')
            ->controller(AssignSupervisorController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::get('form-constants', 'getFormConstants');
                Route::get('supervisors', 'getSupervisors');
                Route::post('', 'store');
                Route::delete('{id}', 'destroy');
                Route::post('delete', 'delete');
                Route::delete('{id}', 'destroy');
            });

        Route::prefix('leave-info')
            ->controller(LeaveController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::get('bulk-actions', 'getBulkActions');
                Route::post('', 'store');
                Route::post('bulk-actions', 'handelBulkActions');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
                Route::get('{id}', 'show');
                Route::delete('{id}', 'destroy');

            });

        Route::prefix('timesheet-submission')
            ->controller(TimesheetSubmissionController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('form-constants', 'formConstants');
                Route::post('get-pay-period-list', 'getPayPeriodList');
                Route::post('get-submitted-timesheet-list', 'getSubmittedTimesheetList');
                Route::post('auto-timesheet-submission', 'storeAutoTimesheet');
                Route::post('delete', 'delete');
                Route::delete('{id}', 'destroy');
            });

        Route::prefix('employer')
            ->controller(EmployerController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::get('form-constants', 'formConstants');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('export', 'exportEmployer');
                Route::post('{id}', 'update');
                Route::delete('{id}', 'destroy');
                Route::put('{id}/restore', 'restoreTrashed');
            });

        Route::prefix('country')
            ->controller(CountryController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('form-constants', 'formConstants');
                Route::delete('{id}', 'destroy');
                Route::post('delete', 'delete');
            });

        Route::prefix('timesheet-submission')
            ->controller(StaffController::class)
            ->group(function () {
                Route::get('staff', 'index');
            });

        Route::prefix('timetable')->controller(TimetableController::class)->group(function () {
            Route::get('', 'index');
            Route::post('', 'store');
            Route::get('get-filter-data', 'getFilterData')->name('spa.timetable.get-filter-data');
            Route::get('{id}', 'show');
            Route::delete('{id}', 'destroy');
            Route::post('delete', 'delete');
            Route::post('export', 'export');
            Route::post('{id}', 'update');
        });

        Route::prefix('setup-services')
            ->controller(SetupServiceController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::delete('{id}', 'destroy');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });
        Route::prefix('setup-service-names')
            ->controller(SetupServicesNameController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::delete('{id}', 'destroy');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });
        Route::prefix('setup-service-categories')
            ->controller(SetupServicesCategoryController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::delete('{id}', 'destroy');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });
        Route::prefix('setup-provider')
            ->controller(SetupProviderController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::delete('{id}', 'destroy');
                Route::post('delete', 'delete');
                Route::post('export', 'export');
                Route::post('{id}', 'update');
            });

        Route::prefix('setup-provider-facility')->controller(\GalaxyAPI\Controllers\SetupProviderFacilityController::class)->group(function () {
            Route::get('', 'index');
            Route::post('', 'store');
            Route::get('{id}', 'show');
            Route::delete('{id}', 'destroy');
            Route::post('delete', 'delete');
            Route::post('{id}', 'update');
        });

        Route::prefix('timetable')->controller(TimetableController::class)->group(function () {
            Route::get('', 'index');
            Route::post('', 'store');
            Route::get('{id}', 'show');
            Route::delete('{id}', 'destroy');
            Route::post('delete', 'delete');
            Route::post('{id}', 'update');
        });
        Route::prefix('timesheet-approval')
            ->controller(TimesheetApprovalController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('pay-periods', 'getPayPeriodList');
                Route::post('approve', 'approve');
            });

        Route::prefix('email-template')
            ->controller(EmailTemplateController::class)
            ->group(function () {
                Route::get('', 'index');
            });
        Route::prefix('college-detail')
            ->controller(CollegeDetailController::class)
            ->group(function () {
                Route::get('', 'index');
            });

        Route::prefix('timesheet-approved')
            ->controller(TimesheetApprovedController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('pay-periods', 'getPayPeriodList');
                Route::post('approved-timesheet-modal', 'getApprovedTimesheetListForModal');
            });
        Route::prefix('countries')
            ->controller(CountryController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
            });

        Route::prefix('semester')
            ->controller(SemesterController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::get('form-constants', 'formConstants');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::delete('{id}', 'destroy');
                Route::post('{id}', 'update');
            });

        Route::prefix('student-training')->controller(StudentTrainingController::class)->group(function () {
            Route::get('', 'index');
            Route::post('', 'store');
            Route::get('{id}', 'show');
            Route::delete('{id}', 'destroy');
            Route::post('delete', 'delete');
            Route::post('{id}', 'update');
        });

        // Admin Agent
        Route::prefix('admin-agent')->controller(AgentController::class)->group(function () {
            Route::get('', 'index')->name('tenant.admin-agent.index');
            Route::post('', 'store');
            Route::get('get-super-agents', 'getSuperAgents');
            Route::get('{id}', 'show');
            Route::delete('{id}', 'destroy');
            Route::post('export', 'export');
            Route::post('delete', 'delete');
            Route::post('save-step', 'saveStep');
            Route::post('{id}', 'update');
            Route::post('{id}/update-status', 'updateAgentStatus')->name('tenant.admin-agent.update-agent-status');
            Route::post('{id}/update-code', 'updateAgentCode')->name('tenant.admin-agent.update-agent-code');
            Route::put('{id}/restore', 'restoreTrashed');
        });

        Route::prefix('agent-staffs')->controller(AgentStaffController::class)->group(function () {
            Route::get('', 'index');
            Route::post('', 'store');
            Route::get('{id}', 'show');
            Route::delete('{id}', 'destroy');
            Route::post('delete', 'delete');
            Route::post('export', 'export');
            Route::post('{id}', 'update');
            Route::post('{id}/update-status', 'updateAgentStatus')->name('tenant.agent-staffs.update-agent-status');
            Route::post('{id}/update-code', 'updateAgentCode')->name('tenant.agent-staffs.update-agent-code');
            Route::put('{id}/restore', 'restoreTrashed');
        });

        Route::prefix('agent-email')->controller(AgentEmailController::class)->group(function () {
            Route::post('', 'store');
        });

        Route::prefix('agent-communication')->controller(AgentCommunicationController::class)->group(function () {
            Route::get('', 'index');
            Route::post('', 'store');
            Route::get('get-form-options', 'getFormOptions');
            Route::get('{id}', 'show');
            Route::post('delete', 'delete');
            Route::delete('{id}', 'destroy');
        });

        Route::prefix('agent-students')->controller(AgentStudentController::class)->group(function () {
            Route::get('', 'index');
            Route::post('export', 'export');
        });

        Route::prefix('agent-payment-history')->controller(AgentPaymentHistoryController::class)->group(function () {
            Route::get('', 'index');
            Route::get('{id}', 'show');
        });

        Route::prefix('student-agent-commission')->controller(StudentAgentCommissionController::class)->group(function () {
            Route::get('', 'index');
        });

        Route::prefix('agent-commission')->controller(AgentCommissionController::class)->group(function () {
            Route::get('', 'index');
            Route::post('bulk', 'addAgentCommissionBulk');
            Route::get('{id}', 'show');
            Route::post('delete', 'delete');
            Route::delete('{id}', 'destroy');
            Route::post('{id}', 'update');
        });

        Route::prefix('agent-status')->controller(AgentStatusController::class)->group(function () {
            Route::get('', 'index');
        });

        Route::prefix('credit-bonus-allocation')->controller(CreditBonusAllocationController::class)->group(function () {
            Route::get('', 'index');
            Route::post('', 'store');
            Route::get('{id}', 'show');
            Route::delete('{id}', 'destroy');
            Route::post('delete', 'delete');
            Route::post('{id}', 'update');
        });

        Route::prefix('student-training-activity-log')->controller(StudentTrainingActivityLogController::class)->group(function () {
            Route::get('activity-types', 'getActivityTypes');
            Route::get('', 'index');
            Route::post('', 'store');
            Route::get('{id}', 'show');
            Route::get('{id}/download', 'downloadFile');
            Route::post('delete', 'delete');
            Route::post('{id}', 'update');
        });

        Route::prefix('setup-section')->controller(SetupSectionController::class)->group(function () {
            Route::get('', 'index');
        });

        Route::prefix('users')->controller(UsersController::class)->group(function () {
            Route::get('get-auth-user', 'getAuthUser');
        });
        Route::prefix('student-interventions')
            ->controller(StudentInterventionController::class)->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('semesters-by-course', 'semesterByCourse');
                Route::get('{id}', 'show');
                Route::post('export', 'export');
                Route::post('notify-student', 'notifyStudent');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
                Route::delete('{id}', 'destroy');
            });

        Route::prefix('collages')
            ->controller(CollegesController::class)->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });
        Route::prefix('courses')
            ->controller(CoursesController::class)->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });
        Route::prefix('intervention-strategies')
            ->controller(InterventionStrategyController::class)->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
                Route::delete('{id}', 'destroy');
            });
        Route::prefix('intervention-types')
            ->controller(InterventionTypeController::class)->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });
        Route::prefix('semesters')
            ->controller(SemesterController::class)->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
                Route::delete('{id}', 'destroy');
            });

        Route::prefix('student-risk-assessment-settings')
            ->controller(StudentRiskAssessmentSettingsController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'saveSettings');
                Route::get('email-templates', 'emailTemplates');
                Route::get('student-course-statuses', 'studentCourseStatuses');
                Route::get('result-grades', 'resultGrades');
                Route::get('categories-config', 'categoriesConfig');
                Route::get('category-parameters', 'getCategoryParameters');
                Route::post('category-parameters', 'saveCategoryParameters');
                Route::post('toggle-category', 'toggleCategory');
            });

        Route::prefix('student-risk-assessments')
            ->controller(StudentRiskAssessmentController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::get('summary', 'summary'); // Summary endpoint for dynamic cards
                Route::get('{id}/details', 'details'); // New detailed view endpoint for popup
                Route::get('export', 'export'); // Export endpoint for Excel download
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });

        Route::prefix('student-risk-assessment-queue')
            ->controller(StudentRiskAssessmentSemesterController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::delete('{id}', 'destroy');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
                Route::delete('{id}', 'destroy');
            });

        Route::prefix('credit-transfer-report')
            ->controller(CreditTransferReportController::class)
            ->group(function () {
                Route::get('export', 'export');
                Route::get('export-pir-template', 'exportPirTemplate');
            });
        Route::prefix('course-subjects')->controller(CourseSubjectsController::class)->group(function () {
            Route::get('', 'index');
        });

        Route::prefix('course-subject')->controller(CourseSubjectController::class)->group(function () {
            Route::get('', 'index');
        });

        Route::prefix('payment-mode')->controller(PaymentModeController::class)->group(function () {
            Route::get('', 'index');
        });

        Route::prefix('student-payment-agent-credit')->controller(StudentPaymentAgentCreditController::class)->group(function () {
            Route::get('', 'index');
        });

        Route::prefix('usermanagement')->group(function () {
            Route::prefix('roles')->controller(SystemRoleController::class)->group(function () {
                Route::get('', 'index');
                //         Route::post('', 'store');
                //         Route::get('{id}', 'show');
                //         Route::post('delete', 'delete');
                //         Route::post('{id}', 'update');
                //         Route::post('{id}/user-id', 'updateUserId');
                //         Route::get('{id}/document', 'getDocument');
                //         Route::post('{id}/create-folder', 'createFolder');
                //         Route::post('{id}/upload-documents', 'uploadDocuments');
                //         Route::put('{id}/rename', 'renameFileFolder');
                //         Route::post('{id}/delete', 'deleteDocument');
                //         Route::post('{id}/move', 'moveDocument');
                //         Route::post('{id}/bookmark', 'bookmarkDocument')->name('spa.teacher.bookmarkDocument');
                //         Route::post('{id}/search', 'searchDocument');
                //         Route::get('{id}/downloadFile/{fileId}', 'downloadFile');
            });
        });
        Route::prefix('departments')
            ->controller(DepartmentController::class)
            ->group(function () {
                Route::get('/', 'index');
            });
        Route::prefix('user-groups')->controller(UserGroupController::class)->group(function () {
            Route::get('', 'index');
            Route::post('', 'store');
            Route::get('{id}', 'show');
            Route::delete('{id}', 'destroy');
            Route::post('delete', 'delete');
            Route::post('{id}', 'update');
            Route::delete('{id}', 'destroy');
        });

        Route::prefix('users')->controller(UsersController::class)->group(function () {
            Route::post('reset-password', 'resetPassword');
            Route::post('disable', 'disableUser');
            Route::post('enable', 'enableUser');
            Route::post('send-invite', 'sendInvite');
            Route::post('assign-roles', 'assignRoles');
            //            Route::get('bulk-actions', 'getBulkActions');
            Route::post('bulk-actions', 'handelBulkActions');
            Route::post('save-step', 'saveStep');
            Route::get('{user_type}/user-meta', 'getUserMetaByType');
            Route::get('{id}/get-assign-roles', 'getAssignedRolesById');
            Route::post('update-status', 'updateStatus');
        });

        Route::prefix('placement-providers')->controller(VpmsPlacementProviderController::class)->group(function () {
            Route::get('', 'index');
            Route::post('', 'store');
            Route::get('{id}', 'show');
            Route::delete('{id}', 'destroy');
            Route::post('delete', 'delete');
            Route::post('export', 'export');
            Route::post('{id}', 'update');
            Route::delete('{id}', 'destroy');
            Route::put('{id}/restore', 'restoreTrashed');

        });

        Route::prefix('certificate-attributes')->controller(CertificateAttributeController::class)->group(function () {
            Route::get('', 'index');
        });

        Route::prefix('permissions')->controller(PermissionController::class)->group(function () {
            Route::get('', 'index');
            Route::get('get-modules', 'getModules');
            Route::post('update-access', 'updateAccess');
        });

        Route::prefix('industries')->controller(IndustriesController::class)->group(function () {
            Route::get('', 'index');
        });

        Route::prefix('agent-document-checklist')->controller(AgentDocumentChecklistController::class)->group(function () {
            Route::get('', 'index');
        });

        Route::prefix('activity-log')->controller(ActivityController::class)->group(function () {
            Route::get('', 'index');
        });

        Route::post('user/{userId}/force-logout', [OtherBrowserSessionsController::class, 'forceLogout'])->name('user.force-logout');

        Route::prefix('user-students')->controller(\GalaxyAPI\Controllers\UserStudentController::class)->group(function () {
            Route::get('', 'index');
            Route::get('{id}', 'show');
            Route::delete('{id}', 'destroy');
            Route::post('delete', 'delete');
            Route::post('export', 'export');
            Route::put('{id}/restore', 'restoreTrashed');
        });
    });
