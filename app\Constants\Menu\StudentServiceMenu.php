<?php

namespace App\Constants\Menu;

use Support\Auth\Access;

class StudentServiceMenu
{
    public const STUDENTSERVICE_MENU_ITEMS = [
        [
            'label' => 'Dashboard',
            'url' => '/dashboard',
            'mainmenu' => ['dashboard', 'administration', 'staff_setting', 'clients', 'partners', 'trainers'],
            'activeurls' => ['user_dashboard', 'vet-fee-help'],
            'svgicon' => '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M1 10L3 8M3 8L10 1L17 8M3 8V18C3 18.5523 3.44772 19 4 19H7M17 8L19 10M17 8V18C17 18.5523 16.5523 19 16 19H13M7 19C7.55228 19 8 18.5523 8 18V14C8 13.4477 8.44772 13 9 13H11C11.5523 13 12 13.4477 12 14V18C12 18.5523 12.4477 19 13 19M7 19H13" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </svg>',
            'permissions' => [
                Access::SPP_DASHBOARD_ACCESS->value,
            ],
        ],
        [
            'label' => 'College Documents',
            'url' => '/view-college-document/0',
            'gap_after' => true,
            'mainmenu' => ['dashboard', 'administration', 'staff_setting', 'clients', 'partners', 'trainers'],
            'activeurls' => ['view-college-document'],
            'svgicon' => '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M3 19H13C14.1046 19 15 18.1046 15 17V7.41421C15 7.149 14.8946 6.89464 14.7071 6.70711L9.29289 1.29289C9.10536 1.10536 8.851 1 8.58579 1H3C1.89543 1 1 1.89543 1 3V17C1 18.1046 1.89543 19 3 19Z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </svg>',
            'permissions' => [
                Access::SPP_COLLEGE_DOCUMENTS_ACCESS->value,
            ],
        ],
        [
            'label' => 'Application',
            'url' => '#',
            'mainmenu' => ['dashboard', 'administration', 'staff_setting', 'clients', 'partners', 'trainers'],
            'activeurls' => [
                'student-online-application',
                'apply-online',
                'apply-online-step2',
                'apply-online-step3',
                'apply-online-step4',
                'apply-online-confirmation',
                'apply-online-final-step',
                'apply-online-step-thank-you',
                'student-saved-application',
                'student-continue-online-application',
                'offer-manage',
                'manage-offer',
                'list-student-document',
                'offer-manage-add-course',
                'offer-manage-communication-log',
                'offer-manage-checklist',
                'offer-manage-document',
                'offer-manage-confirmation',
                'offer-manage-edit',
                'offer-manage-send-mail',
                'offer-manage-upfront-fee-schedule',
                'student-offer-checklist',
                'apply-short-course',
                'apply-short-course-step2',
                'apply-short-course-step-thank-you',
                'offer-generate-invoice',
                'offer-mail',
            ],
            'svgicon' => '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M5 10H11M5 14H11M13 19H3C1.89543 19 1 18.1046 1 17V3C1 1.89543 1.89543 1 3 1H8.58579C8.851 1 9.10536 1.10536 9.29289 1.29289L14.7071 6.70711C14.8946 6.89464 15 7.149 15 7.41421V17C15 18.1046 14.1046 19 13 19Z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </svg>',
            'permissions' => [
                Access::SPP_APPLICATION_ACCESS->value,
            ],
            'sub_menu' => [
                [
                    'label' => 'New Online Application',
                    'url' => '/student-online-application',
                    'permissions' => [
                        Access::SPP_NEW_ONLINE_APPLICATION_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Continue a Saved Online Application',
                    'url' => '/student-continue-online-application',
                    'permissions' => [
                        Access::SPP_CONTINUE_ONLINE_APPLICATION_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Manage Offers',
                    'url' => '/offer-manage',
                    'subactiveurls' => [
                        'list-student-document',
                        'offer-manage-add-course',
                        'offer-manage-communication-log',
                        'offer-manage-checklist',
                        'offer-manage-document',
                        'offer-manage-confirmation',
                        'offer-manage-edit',
                        'offer-manage-send-mail',
                        'offer-manage-upfront-fee-schedule',
                        'student-offer-checklist',
                    ],
                ],
                [
                    'label' => 'Manage Offers Beta',
                    'url' => '/manage-offer',
                    'permissions' => [
                        Access::SPP_MANAGE_OFFERS_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Apply Short Course',
                    'url' => '/apply-short-course',
                    'subactiveurls' => [
                        'apply-short-course-step2',
                        'apply-short-course-step-thank-you',
                    ],
                    'permissions' => [
                        Access::SPP_APPLY_SHORT_COURSE_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Offer Mailing List',
                    'url' => '/offer-mail',
                    'permissions' => [
                        Access::SPP_OFFER_MAILING_LIST_ACCESS->value,
                    ],
                ],
            ],
        ],
        [
            'label' => 'Orientation',
            'url' => 'student-orientation',
            'mainmenu' => ['dashboard', 'administration', 'staff_setting', 'clients', 'partners', 'trainers'],
            'activeurls' => ['student-orientation'],
            'gap_after' => true,
            'svgicon' => '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M17 19V3C17 1.89543 16.1046 1 15 1H5C3.89543 1 3 1.89543 3 3V19M17 19L19 19M17 19H12M3 19L1 19M3 19H8M7 4.99998H8M7 8.99998H8M12 4.99998H13M12 8.99998H13M8 19V14C8 13.4477 8.44772 13 9 13H11C11.5523 13 12 13.4477 12 14V19M8 19H12" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </svg>',
            'permissions' => [
                Access::SPP_ORIENTATION_ACCESS->value,
            ],
        ],
        [
            'label' => 'Cohorts',
            'url' => '#',
            'mainmenu' => ['dashboard', 'administration', 'staff_setting', 'clients', 'partners', 'trainers'],
            'activeurls' => [
                'compliance-intake-group',
                'compliance-studentassign-group',
                'compliance-studentassign-group-management',
                'flexible-timetable-allocation-by-group',
                'student-bulk-enrollment-by-group',
                'bulk-enrollment-by-subject',
            ],
            'svgicon' => '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M10 2.35418C10.7329 1.52375 11.8053 1 13 1C15.2091 1 17 2.79086 17 5C17 7.20914 15.2091 9 13 9C11.8053 9 10.7329 8.47624 10 7.64582M13 19H1V18C1 14.6863 3.68629 12 7 12C10.3137 12 13 14.6863 13 18V19ZM13 19H19V18C19 14.6863 16.3137 12 13 12C11.9071 12 10.8825 12.2922 10 12.8027M11 5C11 7.20914 9.20914 9 7 9C4.79086 9 3 7.20914 3 5C3 2.79086 4.79086 1 7 1C9.20914 1 11 2.79086 11 5Z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </svg>',
            'permissions' => [
                Access::SPP_COHORTS_ACCESS->value,
            ],
            'sub_menu' => [
                [
                    'label' => 'Setup Intake Group',
                    'url' => '/compliance-intake-group',
                    'permissions' => [
                        Access::SPP_SETUP_INTAKE_GROUP_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Assign Student to Group',
                    'url' => '/compliance-studentassign-group',
                    'subactiveurls' => ['compliance-studentassign-group-management'],
                    'permissions' => [
                        Access::SPP_ASSIGN_STUDENT_TO_GROUP_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Bulk Enrollment by Group',
                    'url' => '/student-bulk-enrollment-by-group',
                    'permissions' => [
                        Access::SPP_BULK_ENROLLMENT_BY_GROUP_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Flexible Timetable Allocation by Group',
                    'url' => '/flexible-timetable-allocation-by-group',
                    'permissions' => [
                        Access::SPP_FLEXIBLE_TIMETABLE_ALLOCATION_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Bulk Enrollment by Subject',
                    'url' => '/bulk-enrollment-by-subject',
                    'permissions' => [
                        Access::SPP_BULK_ENROLLMENT_BY_SUBJECT_ACCESS->value,
                    ],
                ],
            ],
        ],
        [
            'label' => 'Attendance',
            'url' => '#',
            'mainmenu' => ['dashboard', 'administration', 'staff_setting', 'clients', 'partners', 'trainers'],
            'activeurls' => [
                'attendance-summary',
                'student-attendance-summary',
                'add-class-attendance',
                'bulk-attendance-weekly',
                'report-and-warnings',
                'bulk-attendance',
                'student-mail-waring-setting',
                'bulk-attendance-subject',
                'student-mail-percentage-waring-setting',
            ],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path class="stroke-none newicon"  d="M11.3135 15.5002C11.4859 14.9667 11.7253 14.4634 12.0219 14.0002H4.25278C3.01076 14.0002 2.00391 15.007 2.00391 16.2491V16.8267C2.00391 17.7195 2.32242 18.583 2.90219 19.2619C4.46849 21.0962 6.8545 22.0013 10.0004 22.0013C10.9314 22.0013 11.7961 21.922 12.5927 21.7629C12.2335 21.3496 11.9256 20.8906 11.6789 20.3957C11.1555 20.466 10.5962 20.5013 10.0004 20.5013C7.26206 20.5013 5.29618 19.7555 4.04287 18.2878C3.69502 17.8805 3.50391 17.3624 3.50391 16.8267V16.2491C3.50391 15.8355 3.83919 15.5002 4.25278 15.5002H11.3135ZM10.0004 2.00488C12.7618 2.00488 15.0004 4.24346 15.0004 7.00488C15.0004 9.76631 12.7618 12.0049 10.0004 12.0049C7.23894 12.0049 5.00036 9.76631 5.00036 7.00488C5.00036 4.24346 7.23894 2.00488 10.0004 2.00488ZM10.0004 3.50488C8.06737 3.50488 6.50036 5.07189 6.50036 7.00488C6.50036 8.93788 8.06737 10.5049 10.0004 10.5049C11.9334 10.5049 13.5004 8.93788 13.5004 7.00488C13.5004 5.07189 11.9334 3.50488 10.0004 3.50488ZM17.5 12.0002C20.5376 12.0002 23 14.4627 23 17.5002C23 20.5378 20.5376 23.0002 17.5 23.0002C14.4624 23.0002 12 20.5378 12 17.5002C12 14.4627 14.4624 12.0002 17.5 12.0002ZM19.5 17.5003H17.5L17.5 15.0002C17.5 14.724 17.2761 14.5002 17 14.5002C16.7239 14.5002 16.5 14.724 16.5 15.0002L16.5 17.9988L16.5 18.0003C16.5 18.2764 16.7239 18.5003 17 18.5003H19.5C19.7761 18.5003 20 18.2764 20 18.0003C20 17.7242 19.7761 17.5003 19.5 17.5003Z" fill="#9CA3AF"/>
            </svg>',
            'permissions' => [
                Access::SPP_ATTENDANCE_ACCESS->value,
            ],
            'sub_menu' => [
                [
                    'label' => 'Attendance Summary',
                    'url' => '/attendance-summary',
                    'subactiveurls' => ['student-attendance-summary'],
                    'permissions' => [
                        Access::SPP_ATTENDANCE_SUMMARY_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Add Class Attendance (Daily/Weekly)',
                    'url' => '/add-class-attendance',
                    'permissions' => [
                        Access::SPP_ADD_CLASS_ATTENDANCE_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Bulk Attendance',
                    'url' => '/bulk-attendance-weekly',
                    'subactiveurls' => ['bulk-attendance-subject'],
                    'permissions' => [
                        Access::SPP_BULK_ATTENDANCE_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Reports',
                    'url' => '/report-and-warnings',
                    'permissions' => [
                        Access::SPP_REPORTS_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Warning Days setting',
                    'url' => '/student-mail-waring-setting',
                    'permissions' => [
                        Access::SPP_WARNING_DAYS_SETTING_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Warning Percentage Setting',
                    'url' => '/student-mail-percentage-waring-setting',
                    'permissions' => [
                        Access::SPP_WARNING_PERCENTAGE_SETTING_ACCESS->value,
                    ],
                ],
            ],
        ],
        [
            'label' => 'Competency',
            'url' => '#',
            'mainmenu' => ['dashboard', 'administration', 'staff_setting', 'clients', 'partners', 'trainers'],
            'activeurls' => [
                'setup-assessment-task',
                'task-entry',
                'task-results-entry',
                'transfer-results',
                'vocational-placement-result',
                'transfer-results-by-unit',
            ],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path class="stroke-none newicon" d="M17.9297 9.91797C18 10.0586 18.0352 10.2109 18.0352 10.375C18.0352 10.6797 17.918 10.9492 17.6836 11.1836C17.4727 11.3945 17.2148 11.5 16.9102 11.5H15.7852V13.75C15.7852 14.3594 15.5625 14.8867 15.1172 15.332C14.6719 15.7773 14.1445 16 13.5352 16H12.4102V17.6875C12.4102 17.8516 12.3516 17.9805 12.2344 18.0742C12.1406 18.1914 12.0117 18.25 11.8477 18.25H11.2852C11.1211 18.25 10.9805 18.1914 10.8633 18.0742C10.7695 17.9805 10.7227 17.8516 10.7227 17.6875V14.3125H13.5352C13.6992 14.3125 13.8281 14.2656 13.9219 14.1719C14.0391 14.0547 14.0977 13.9141 14.0977 13.75V9.8125H16.0664C15.9258 9.4375 15.7031 8.80469 15.3984 7.91406C14.7188 5.89844 14.25 4.70313 13.9922 4.32812C13.5234 3.67187 12.832 3.10937 11.918 2.64062C11.0273 2.17188 10.1836 1.9375 9.38672 1.9375H7.03125C6.02344 1.9375 5.08594 2.18359 4.21875 2.67578C3.375 3.16797 2.73047 3.84766 2.28516 4.71484C1.74609 5.79297 1.58203 6.88281 1.79297 7.98438C2.02734 9.08594 2.56641 10.0117 3.41016 10.7617L3.97266 11.2539V17.6875C3.97266 17.8516 3.91406 17.9805 3.79688 18.0742C3.70312 18.1914 3.57422 18.25 3.41016 18.25H2.84766C2.68359 18.25 2.54297 18.1914 2.42578 18.0742C2.33203 17.9805 2.28516 17.8516 2.28516 17.6875V12.0273C1.67578 11.4648 1.14844 10.7031 0.703125 9.74219C0.257812 8.75781 0.0351562 7.84375 0.0351562 7C0.0351562 6.90625 0.0351562 6.82422 0.0351562 6.75391C0.105469 4.92578 0.832031 3.39062 2.21484 2.14844C3.59766 0.882812 5.21484 0.25 7.06641 0.25H9.38672C10.0664 0.25 10.793 0.390625 11.5664 0.671875C12.3633 0.953125 13.0898 1.32813 13.7461 1.79688C14.4258 2.26562 14.9648 2.78125 15.3633 3.34375C15.6211 3.71875 15.9023 4.32812 16.207 5.17188C16.5352 5.99219 16.8516 6.88281 17.1562 7.84375C17.4844 8.80469 17.7422 9.49609 17.9297 9.91797ZM11.0742 7H9.5625C9.65625 7.1875 9.70312 7.375 9.70312 7.5625C9.70312 7.9375 9.57422 8.25391 9.31641 8.51172C9.05859 8.76953 8.74219 8.89844 8.36719 8.89844C8.20312 8.89844 8.05078 8.875 7.91016 8.82812V10.375H6.11719V8.82812C5.97656 8.875 5.82422 8.89844 5.66016 8.89844C5.28516 8.89844 4.96875 8.76953 4.71094 8.51172C4.45312 8.25391 4.32422 7.9375 4.32422 7.5625C4.32422 7.5625 4.32422 7.55078 4.32422 7.52734C4.32422 7.50391 4.32422 7.49219 4.32422 7.49219C3.71484 7.28125 3.41016 6.85938 3.41016 6.22656C3.41016 5.85156 3.53906 5.53516 3.79688 5.27734C4.07812 4.99609 4.39453 4.85547 4.74609 4.85547C4.74609 4.48047 4.875 4.16406 5.13281 3.90625C5.41406 3.64844 5.74219 3.51953 6.11719 3.51953C6.32812 3.51953 6.55078 3.58984 6.78516 3.73047C7.04297 3.28516 7.41797 3.0625 7.91016 3.0625C8.40234 3.0625 8.77734 3.28516 9.03516 3.73047C9.26953 3.58984 9.49219 3.51953 9.70312 3.51953C9.96094 3.51953 10.207 3.61328 10.4414 3.80078C10.6992 3.96484 10.875 4.16406 10.9688 4.39844H11.0742C11.4492 4.39844 11.7656 4.52734 12.0234 4.78516C12.2812 5.04297 12.4102 5.34766 12.4102 5.69922C12.4102 5.93359 12.3516 6.15625 12.2344 6.36719C12.1172 6.55469 11.9531 6.70703 11.7422 6.82422C11.5312 6.94141 11.3086 7 11.0742 7Z" fill="#9CA3AF"/> </svg>',
            'sub_menu' => [
                [
                    'label' => 'Attendance Summary',
                    'url' => '/setup-assessment-task',
                    'permissions' => [
                        Access::SPP_SETUP_ASSESSMENT_TASK_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Task Entry',
                    'url' => '/task-entry',
                    'permissions' => [
                        Access::SPP_TASK_ENTRY_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Task Results Entry',
                    'url' => '/task-results-entry',
                    'permissions' => [
                        Access::SPP_TASK_RESULTS_ENTRY_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Transfer Results',
                    'url' => '/transfer-results',
                    'permissions' => [
                        Access::SPP_TRANSFER_RESULTS_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Vocational Placement Result',
                    'url' => '/vocational-placement-result',
                    'permissions' => [
                        Access::SPP_VOCATIONAL_PLACEMENT_RESULT_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Transfer Results by Unit',
                    'url' => '/transfer-results-by-unit',
                    'permissions' => [
                        Access::SPP_TRANSFER_RESULTS_BY_UNIT_ACCESS->value,
                    ],
                ],
            ],
        ],
        [
            'label' => 'Certificate',
            'url' => 'generate-bulk-certificate',
            'mainmenu' => ['dashboard', 'administration', 'staff_setting', 'clients', 'partners', 'trainers'],
            'activeurls' => ['generate-bulk-certificate'],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                               <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                          </svg>',
            'permissions' => [
                Access::SPP_CERTIFICATE_ACCESS->value,
            ],
        ],
        [
            'label' => 'Intervention',
            'url' => '/compliance-intervention',
            'mainmenu' => ['dashboard', 'administration', 'staff_setting', 'clients', 'partners', 'trainers'],
            'activeurls' => ['compliance-intervention', 'edit-student-intervention', 'add-intervention'],
            'svgicon' => '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M18.6179 4.98434C18.4132 4.99472 18.2072 4.99997 18 4.99997C14.9265 4.99997 12.123 3.84453 9.99993 1.94434C7.87691 3.84446 5.07339 4.99985 2 4.99985C1.79277 4.99985 1.58678 4.9946 1.38213 4.98422C1.1327 5.94783 1 6.95842 1 8.00001C1 13.5915 4.82432 18.2898 10 19.622C15.1757 18.2898 19 13.5915 19 8.00001C19 6.95847 18.8673 5.94791 18.6179 4.98434Z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </svg>',
            'permissions' => [
                Access::SPP_INTERVENTION_ACCESS->value,
            ],
        ],
        [
            'label' => 'Traineeship',
            'url' => '/trineeship-visits',
            'gap_after' => true,
            'mainmenu' => ['dashboard', 'administration', 'staff_setting', 'clients', 'partners', 'trainers'],
            'activeurls' => ['trineeship-visits'],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                               <path d="M12 14l9-5-9-5-9 5 9 5z" /> <path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" /> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
                          </svg>',
            'permissions' => [
                Access::SPP_TRAINEESHIP_ACCESS->value,
            ],
        ],
        [
            'label' => 'Service management',
            'url' => '#',
            'mainmenu' => ['dashboard', 'administration', 'staff_setting', 'clients', 'partners', 'trainers'],
            'activeurls' => ['additional-service-fee', 'additional-service-provider', 'allocate-provider'],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>',
            'permissions' => [
                Access::SPP_SERVICE_MANAGEMENT_ACCESS->value,
            ],
            'sub_menu' => [
                [
                    'label' => 'Service fee setup',
                    'url' => '/additional-service-fee',
                    'permissions' => [
                        Access::SPP_SERVICE_FEE_SETUP_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Provider setup',
                    'url' => '/additional-service-provider',
                    'permissions' => [
                        Access::SPP_PROVIDER_SETUP_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Allocate provider',
                    'url' => '/allocate-provider',
                    'permissions' => [
                        Access::SPP_ALLOCATE_PROVIDER_ACCESS->value,
                    ],
                ],
            ],
        ],
        [
            'label' => 'Bulk Update',
            'url' => '#',
            'mainmenu' => ['dashboard', 'administration', 'staff_setting', 'clients', 'partners', 'trainers'],
            'activeurls' => [
                'bulk-update-result',
                'update-unit-outcome',
                'update-student-unit-result',
                'update-student-result',
                'update-student-activity',
                'bulk-completion-update',
                'bulk-update-student-course',
                'bulk-update-course-template',
            ],
            'permissions' => [
                Access::SPP_BULK_UPDATE_ACCESS->value,
            ],
            'svgicon' => '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                            </svg>',
            'sub_menu' => [
                [
                    'label' => 'Update final outcome',
                    'url' => '/bulk-update-result',
                    'subactiveurls' => ['update-unit-outcome', 'update-student-unit-result', 'update-student-result', 'update-student-activity'],
                    'permissions' => [
                        Access::SPP_UPDATE_FINAL_OUTCOME_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Update student course status',
                    'url' => '/bulk-completion-update',
                    'permissions' => [
                        Access::SPP_UPDATE_STUDENT_COURSE_STATUS_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Update student course',
                    'url' => '/bulk-update-student-course',
                    'permissions' => [
                        Access::SPP_UPDATE_STUDENT_COURSE_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Update student course template',
                    'url' => '/bulk-update-course-template',
                    'permissions' => [
                        Access::SPP_UPDATE_STUDENT_COURSE_TEMPLATE_ACCESS->value,
                    ],
                ],
            ],
        ],
        [
            'label' => 'Others',
            'url' => '#',
            'mainmenu' => ['dashboard', 'administration', 'staff_setting', 'clients', 'partners', 'trainers'],
            'activeurls' => [
                'view-leave-list',
                'add-leave-info',
                'edit-leave-info',
                'user_profile',
                'view-elearning-link-list',
                'add-elearning-link',
                'edit-elearning-link',
                'view-register-improvement',
                'edit-register-improvement',
                'add-register-improvement',
                'prisms-data-validation',
                'generate-reports',
                'generate-reports-letter',
                'communication',
            ],
            'permissions' => [
                Access::SPP_OTHERS_ACCESS->value,
            ],
            'svgicon' => '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M4 17L8 1M10 17L14 1M3 6H17M1 12H15" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </svg>',
            'sub_menu' => [
                [
                    'label' => 'Validate PRISMS',
                    'url' => '/prisms-data-validation',
                    'permissions' => [
                        Access::SPP_PRISMS_DATA_VALIDATION_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Generate reports',
                    'url' => '/generate-reports',
                    'permissions' => [
                        Access::SPP_GENERATE_REPORTS_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Communication',
                    'url' => '/communication/all',
                    'subactiveurls' => ['communication'],
                    'permissions' => [
                        Access::SPP_COMMUNICATION_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Continuous Improvement',
                    'url' => '/view-register-improvement',
                    'subactiveurls' => ['add-register-improvement', 'edit-register-improvement'],
                    'permissions' => [
                        Access::SPP_CONTINUOUS_IMPROVEMENT_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Forms',
                    'url' => '/view-elearning-link-list',
                    'subactiveurls' => ['add-elearning-link', 'edit-elearning-link'],
                    'permissions' => [
                        Access::SPP_FORMS_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Edit profile',
                    'url' => '/profile',
                    'permissions' => [
                        Access::SPP_EDIT_PROFILE_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Leave info',
                    'url' => '/view-leave-list',
                    'subactiveurls' => ['add-leave-info', 'edit-leave-info'],
                    'permissions' => [
                        Access::SPP_LEAVE_INFO_ACCESS->value,
                    ],
                ],
            ],
        ],
    ];
}
