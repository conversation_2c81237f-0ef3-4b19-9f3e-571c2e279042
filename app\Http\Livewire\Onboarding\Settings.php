<?php

namespace App\Http\Livewire\Onboarding;

use Domains\Customers\Settings\DTO\TrackingForm;
use Domains\Customers\Settings\Models\SettingTracker;
use Livewire\Component;
use Support\Auth\Permission;

class Settings extends Component
{
    public $collection;

    private $searchedGroup = null;

    public $keyword = '';

    public function mount()
    {
        // dd($this->collection);
        $this->search();
    }

    public function init()
    {
        $this->collection = fromMemory('settingTracker', function () {
            return SettingTracker::all();
        });
    }

    public function updatedKeyword($val)
    {
        $this->search($val);
    }

    public function search($value = '')
    {
        $this->init();
        // dd($this->collection);
        if ($value) {

            $list = $this->collection->mapped(false)->filter(function (TrackingForm $item) use ($value) {
                $groupLabel = config('onboarding.forms.'.$item->group.'.label');

                return strpos(strtolower($item->label), strtolower($value)) !== false || strpos(strtolower($groupLabel), strtolower($value)) !== false;
            });

            // dd($list);
            $this->searchedGroup = $this->collection->groups($list);

            return;
        }

        $activeRole = auth()->user()->currentActiveRole();
        // dd($this->collection->mapped(false));
        $this->searchedGroup = $this->collection->groups($this->collection->mapped(false)
            ->filter(function (TrackingForm $item) use ($activeRole) {
                return auth()->user()->isAdmin() || ($item->permission && $activeRole ? $activeRole->hasPermissionTo($item->permission, Permission::DEFAULT_GUARD) : true);
            }));
    }

    public function render()
    {
        return view('onboarding.livewire.settings.index', [
            'groups' => $this->searchedGroup,
        ]);
    }
}
